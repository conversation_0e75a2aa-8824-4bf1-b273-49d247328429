import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from app.cache import C<PERSON><PERSON>ervice
from app.cached_parser import CachedCVParserWorkflow
from app.config import settings
from app.models import CVData, PersonalInfo
from app.parser import CVParserState


class TestCacheService:
    """Test cases for CacheService"""

    @pytest.fixture
    def mock_redis_client(self):
        """Mock Redis client for testing"""
        mock_client = AsyncMock()
        mock_client.ping = AsyncMock(return_value=True)
        mock_client.get = AsyncMock(return_value=None)
        mock_client.setex = AsyncMock(return_value=True)
        mock_client.delete = AsyncMock(return_value=1)
        mock_client.keys = AsyncMock(return_value=[])
        mock_client.dbsize = AsyncMock(return_value=0)
        mock_client.info = AsyncMock(
            return_value={"used_memory_human": "1MB", "connected_clients": 1}
        )
        mock_client.close = AsyncMock()
        return mock_client

    @pytest.fixture
    def cache_service_instance(self):
        """Create a fresh cache service instance for testing"""
        return CacheService()

    @pytest.fixture
    def sample_cv_state(self):
        """Sample CVParserState for testing"""
        return CVParserState(
            input_text="John Doe\nSoftware Engineer\<EMAIL>",
            input_file_base64=None,
            filename="test_cv.txt",
            parsed_data=None,
            errors=[],
            mode="text",
            multimodal_full_result=None,
            validation_result=None,
            personal_info_result=None,
            summary_result=None,
            education_result=None,
            skills_result=None,
            work_experience_result=None,
            projects_result=None,
            certifications_result=None,
            additional_sections_result=None,
        )

    @pytest.fixture
    def sample_cvdata(self):
        """Sample CVData for testing"""
        return CVData(
            personal_info=PersonalInfo(full_name="John Doe", email="<EMAIL>")
        )

    def test_generate_cache_key_text_mode(
        self, cache_service_instance, sample_cv_state
    ):
        """Test cache key generation for text mode"""
        cache_key = cache_service_instance.generate_cache_key(sample_cv_state)

        assert cache_key.startswith(settings.CACHE_KEY_PREFIX)
        assert ":text:" in cache_key
        assert len(cache_key.split(":")) == 4  # prefix:mode:hash:model_version

    def test_generate_cache_key_multimodal_mode(self, cache_service_instance):
        """Test cache key generation for multimodal mode"""
        state = CVParserState(
            input_text=None,
            input_file_base64="base64encodeddata",
            filename="test.pdf",
            parsed_data=None,
            errors=[],
            mode="multimodal",
            multimodal_full_result=None,
            validation_result=None,
            personal_info_result=None,
            summary_result=None,
            education_result=None,
            skills_result=None,
            work_experience_result=None,
            projects_result=None,
            certifications_result=None,
            additional_sections_result=None,
        )

        cache_key = cache_service_instance.generate_cache_key(state)

        assert cache_key.startswith(settings.CACHE_KEY_PREFIX)
        assert ":multimodal:" in cache_key

    def test_generate_cache_key_empty_content(self, cache_service_instance):
        """Test cache key generation with empty content"""
        state = CVParserState(
            input_text="",
            input_file_base64=None,
            filename="empty.txt",
            parsed_data=None,
            errors=[],
            mode="text",
            multimodal_full_result=None,
            validation_result=None,
            personal_info_result=None,
            summary_result=None,
            education_result=None,
            skills_result=None,
            work_experience_result=None,
            projects_result=None,
            certifications_result=None,
            additional_sections_result=None,
        )

        cache_key = cache_service_instance.generate_cache_key(state)

        # Should return error key for empty content
        assert ":error:no_key" in cache_key

    @pytest.mark.asyncio
    @patch("app.cache.redis.Redis")
    @patch("app.cache.redis.ConnectionPool")
    async def test_connect_success(
        self,
        mock_pool_class,
        mock_redis_class,
        cache_service_instance,
        mock_redis_client,
    ):
        """Test successful Redis connection"""
        # Enable cache for test
        with patch("app.config.settings.CACHE_ENABLED", True):
            mock_pool_class.from_url.return_value = MagicMock()
            mock_redis_class.return_value = mock_redis_client

            result = await cache_service_instance.connect()

            assert result is True
            assert cache_service_instance.is_available() is True

    @pytest.mark.asyncio
    @patch("app.cache.redis.Redis")
    async def test_connect_failure(self, mock_redis_class, cache_service_instance):
        """Test Redis connection failure"""
        # Enable cache for test
        with patch("app.config.settings.CACHE_ENABLED", True):
            mock_redis_client = AsyncMock()
            mock_redis_client.ping.side_effect = Exception("Connection failed")
            mock_redis_class.return_value = mock_redis_client

            result = await cache_service_instance.connect()

            assert result is False
            assert cache_service_instance.is_available() is False

    @pytest.mark.asyncio
    async def test_cache_disabled(self, cache_service_instance):
        """Test behavior when cache is disabled"""
        with patch.object(settings, "CACHE_ENABLED", False):
            result = await cache_service_instance.connect()

            assert result is False
            assert cache_service_instance.is_available() is False

    @pytest.mark.asyncio
    async def test_get_cached_result_miss(
        self, cache_service_instance, mock_redis_client
    ):
        """Test cache miss scenario"""
        with patch("app.config.settings.CACHE_ENABLED", True):
            cache_service_instance._redis_client = mock_redis_client
            cache_service_instance._is_connected = True
            mock_redis_client.get.return_value = None

            result = await cache_service_instance.get_cached_result("test_key")

            assert result is None
            mock_redis_client.get.assert_called_once_with("test_key")

    @pytest.mark.asyncio
    async def test_get_cached_result_hit(
        self, cache_service_instance, mock_redis_client
    ):
        """Test cache hit scenario"""
        with patch("app.config.settings.CACHE_ENABLED", True):
            cache_service_instance._redis_client = mock_redis_client
            cache_service_instance._is_connected = True

            cached_data = (
                '{"parsed_data": {"personal_info": {"full_name": "John Doe"}}}'
            )
            mock_redis_client.get.return_value = cached_data

            result = await cache_service_instance.get_cached_result("test_key")

            assert result is not None
            assert "parsed_data" in result
            mock_redis_client.get.assert_called_once_with("test_key")

    @pytest.mark.asyncio
    async def test_cache_result_success(
        self, cache_service_instance, mock_redis_client, sample_cvdata
    ):
        """Test successful result caching"""
        with patch("app.config.settings.CACHE_ENABLED", True):
            cache_service_instance._redis_client = mock_redis_client
            cache_service_instance._is_connected = True

            result_data = {"parsed_data": sample_cvdata}

            success = await cache_service_instance.cache_result(
                "test_key", result_data, is_error=False
            )

            assert success is True
            mock_redis_client.setex.assert_called_once()

            # Check that TTL for success was used
            call_args = mock_redis_client.setex.call_args
            assert call_args[0][1] == settings.CACHE_TTL_SUCCESS

    @pytest.mark.asyncio
    async def test_cache_result_error(self, cache_service_instance, mock_redis_client):
        """Test that error results still use success TTL (errors are not cached at higher level)"""
        with patch("app.config.settings.CACHE_ENABLED", True):
            cache_service_instance._redis_client = mock_redis_client
            cache_service_instance._is_connected = True

            error_data = {"errors": ["Test error"], "parsed_data": None}

            success = await cache_service_instance.cache_result(
                "test_key", error_data, is_error=True
            )

            assert success is True
            mock_redis_client.setex.assert_called_once()

            # Check that success TTL is used (no separate error TTL)
            call_args = mock_redis_client.setex.call_args
            assert call_args[0][1] == settings.CACHE_TTL_SUCCESS

    @pytest.mark.asyncio
    async def test_clear_cache_by_pattern(
        self, cache_service_instance, mock_redis_client
    ):
        """Test cache clearing by pattern"""
        with patch("app.config.settings.CACHE_ENABLED", True):
            cache_service_instance._redis_client = mock_redis_client
            cache_service_instance._is_connected = True

            test_keys = ["cv_parser:text:abc123", "cv_parser:multimodal:def456"]
            mock_redis_client.keys.return_value = test_keys
            mock_redis_client.delete.return_value = len(test_keys)

            cleared_count = await cache_service_instance.clear_cache_by_pattern(
                "cv_parser:*"
            )

            assert cleared_count == 2
            mock_redis_client.keys.assert_called_once_with("cv_parser:*")
            mock_redis_client.delete.assert_called_once_with(*test_keys)


class TestCachedCVParserWorkflow:
    """Test cases for CachedCVParserWorkflow"""

    @pytest.fixture
    def cached_workflow(self):
        """Create a fresh cached workflow instance"""
        return CachedCVParserWorkflow()

    @pytest.fixture
    def sample_cvdata(self):
        """Sample CVData for testing"""
        return CVData(
            personal_info=PersonalInfo(full_name="John Doe", email="<EMAIL>")
        )

    @pytest.fixture
    def sample_workflow_result(self, sample_cvdata):
        """Sample workflow result"""
        return {
            "parsed_data": sample_cvdata,
            "errors": [],
            "error": None,
        }

    @pytest.fixture
    def sample_cv_state(self):
        """Sample CVParserState for testing"""
        return CVParserState(
            input_text="John Doe\nSoftware Engineer\<EMAIL>",
            input_file_base64=None,
            filename="test_cv.txt",
            parsed_data=None,
            errors=[],
            mode="text",
            multimodal_full_result=None,
            validation_result=None,
            personal_info_result=None,
            summary_result=None,
            education_result=None,
            skills_result=None,
            work_experience_result=None,
            projects_result=None,
            certifications_result=None,
            additional_sections_result=None,
        )

    @pytest.mark.asyncio
    @patch("app.cached_parser.cache_service")
    @patch("app.cached_parser.cv_parser_workflow")
    async def test_cache_miss_workflow_execution(
        self,
        mock_workflow,
        mock_cache_service,
        cached_workflow,
        sample_cv_state,
        sample_workflow_result,
    ):
        """Test workflow execution on cache miss"""
        # Setup mocks
        mock_cache_service.generate_cache_key.return_value = "test_key"
        mock_cache_service.get_cached_result = AsyncMock(
            return_value=None
        )  # Cache miss
        mock_cache_service.cache_result = AsyncMock(return_value=True)
        mock_workflow.ainvoke = AsyncMock(return_value=sample_workflow_result)

        result = await cached_workflow.ainvoke(sample_cv_state)

        # Verify workflow was executed
        mock_workflow.ainvoke.assert_called_once_with(sample_cv_state)

        # Verify result was cached
        mock_cache_service.cache_result.assert_called_once()

        assert result == sample_workflow_result

    @pytest.mark.asyncio
    @patch("app.cached_parser.cache_service")
    @patch("app.cached_parser.cv_parser_workflow")
    async def test_cache_hit_no_workflow_execution(
        self,
        mock_workflow,
        mock_cache_service,
        cached_workflow,
        sample_cv_state,
        sample_workflow_result,
    ):
        """Test cache hit scenario where workflow is not executed"""
        # Setup mocks
        mock_cache_service.generate_cache_key.return_value = "test_key"
        mock_cache_service.get_cached_result = AsyncMock(
            return_value=sample_workflow_result  # Cache hit
        )

        result = await cached_workflow.ainvoke(sample_cv_state)

        # Verify workflow was NOT executed
        mock_workflow.ainvoke.assert_not_called()

        assert result == sample_workflow_result

    @pytest.mark.asyncio
    @patch("app.cached_parser.cache_service")
    @patch("app.cached_parser.cv_parser_workflow")
    async def test_workflow_error_not_cached(
        self, mock_workflow, mock_cache_service, cached_workflow, sample_cv_state
    ):
        """Test that workflow errors are NOT cached"""
        # Setup mocks
        mock_cache_service.generate_cache_key.return_value = "test_key"
        mock_cache_service.get_cached_result = AsyncMock(
            return_value=None
        )  # Cache miss
        mock_cache_service.cache_result = AsyncMock(return_value=True)

        # Simulate workflow error
        mock_workflow.ainvoke = AsyncMock(side_effect=Exception("Workflow failed"))

        with pytest.raises(Exception, match="Workflow failed"):
            await cached_workflow.ainvoke(sample_cv_state)

        # Verify error result was NOT cached
        mock_cache_service.cache_result.assert_not_called()

    @pytest.mark.asyncio
    @patch("app.cached_parser.cache_service")
    @patch("app.cached_parser.cv_parser_workflow")
    async def test_workflow_result_error_not_cached(
        self, mock_workflow, mock_cache_service, cached_workflow, sample_cv_state
    ):
        """Test that workflow results with errors are NOT cached"""
        # Setup mocks
        mock_cache_service.generate_cache_key.return_value = "test_key"
        mock_cache_service.get_cached_result = AsyncMock(
            return_value=None
        )  # Cache miss
        mock_cache_service.cache_result = AsyncMock(return_value=True)

        # Simulate workflow returning error result
        error_result = {
            "errors": ["Parsing failed"],
            "parsed_data": None,
            "error": "Test error",
        }
        mock_workflow.ainvoke = AsyncMock(return_value=error_result)

        result = await cached_workflow.ainvoke(sample_cv_state)

        # Verify result returned correctly
        assert result == error_result

        # Verify error result was NOT cached
        mock_cache_service.cache_result.assert_not_called()


@pytest.mark.asyncio
class TestCacheIntegration:
    """Integration tests for cache functionality"""

    async def test_cache_service_availability_when_disabled(self):
        """Test cache service behavior when disabled"""
        with patch.object(settings, "CACHE_ENABLED", False):
            service = CacheService()
            connected = await service.connect()

            assert connected is False
            assert service.is_available() is False

    async def test_cache_service_graceful_failure(self):
        """Test cache service graceful failure handling"""
        service = CacheService()

        # Test with invalid Redis URL
        with patch.object(settings, "REDIS_URL", "redis://invalid:9999/0"):
            connected = await service.connect()

            assert connected is False
            assert service.is_available() is False
