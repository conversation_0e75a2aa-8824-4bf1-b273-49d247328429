"""API endpoint tests for CV Parser API."""

from io import Bytes<PERSON>
from unittest.mock import AsyncMock, patch

from fastapi.testclient import TestClient

from app.file_processors import FallbackResult
from app.models import CVData


class TestHealthEndpoint:
    """Test the health check endpoint."""

    def test_health_check(self, client: TestClient):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["version"] == "1.2.1"
        assert data["model"] == "gpt-4o-mini"

    def test_health_endpoint_structure(self, client: TestClient):
        """Test health endpoint response structure."""
        response = client.get("/health")
        data = response.json()
        required_fields = ["status", "version", "model"]
        for field in required_fields:
            assert field in data


class TestParseEndpoint:
    """Test the main parse endpoint."""

    @patch("app.api.cached_cv_parser_workflow")
    @patch("app.api.process_file_with_fallback", new_callable=AsyncMock)
    def test_file_upload_text_extraction(
        self,
        mock_process_file_with_fallback,
        mock_workflow,
        client: TestClient,
        sample_parsed_cv: CVData,
    ):
        """Test file upload with text extraction."""
        # Setup mocks
        mock_process_file_with_fallback.return_value = FallbackResult(
            text="Sample CV text",
            filename="test.pdf",
            mode_used="text",
            fallback_triggered=False,
            fallback_reason=None,
            file_base64=None,
        )
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Create test file
        file_content = b"fake pdf content"
        files = {"file": ("test.pdf", BytesIO(file_content), "application/pdf")}

        response = client.post("/internal/v1/parse", files=files)

        assert response.status_code == 200
        data = response.json()

        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data

        # Check profile contains the parsed CV data
        profile = data["profile"]
        assert profile["personal_info"]["full_name"] == "John Doe"
        assert profile["personal_info"]["email"] == "<EMAIL>"

        # Check meta_data structure
        assert "cv_text" in data["meta_data"]
        assert "parser_time" in data["meta_data"]
        # cv_text is now empty in the response (not included for privacy/size reasons)

        mock_process_file_with_fallback.assert_called_once()

    @patch("app.api.cached_cv_parser_workflow")
    @patch("app.api.process_file_with_fallback", new_callable=AsyncMock)
    def test_file_upload_multimodal(
        self,
        mock_process_file_with_fallback,
        mock_workflow,
        client: TestClient,
        sample_parsed_cv: CVData,
    ):
        """Test file upload with multimodal parsing."""
        # Setup mocks for forced multimodal
        mock_process_file_with_fallback.return_value = FallbackResult(
            text=None,
            filename="test.pdf",
            mode_used="multimodal",
            fallback_triggered=False,
            fallback_reason="Forced multimodal mode",
            file_base64="base64content",
        )
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Create test file
        file_content = b"fake pdf content"
        files = {
            "file": ("test.pdf", BytesIO(file_content), "application/pdf"),
            "multimodal": (None, "true"),
        }

        response = client.post("/internal/v1/parse", files=files)

        assert response.status_code == 200
        data = response.json()

        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data

        # Check profile contains the parsed CV data
        profile = data["profile"]
        assert profile["personal_info"]["full_name"] == "John Doe"

        # Check meta_data structure
        assert "cv_text" in data["meta_data"]
        assert "parser_time" in data["meta_data"]

        mock_process_file_with_fallback.assert_called_once()

    @patch("app.api.cached_cv_parser_workflow")
    def test_raw_text_parsing(
        self,
        mock_workflow,
        client: TestClient,
        sample_parsed_cv: CVData,
        sample_cv_text: str,
    ):
        """Test raw text parsing."""
        # Setup mock
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Test data
        request_data = {"text": sample_cv_text, "filename": "test.txt"}

        response = client.post("/internal/v1/parse", json=request_data)

        assert response.status_code == 200
        data = response.json()

        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data

        # Check profile contains the parsed CV data
        profile = data["profile"]
        assert profile["personal_info"]["full_name"] == "John Doe"

        # Check meta_data structure
        assert "cv_text" in data["meta_data"]
        assert "parser_time" in data["meta_data"]
        # cv_text is now empty in the response

    def test_no_input_provided(self, client: TestClient):
        """Test error when no input is provided."""
        response = client.post("/internal/v1/parse")
        assert response.status_code == 400
        assert (
            "Either file upload or text in request body must be provided"
            in response.text
        )

    def test_invalid_file_type(self, client: TestClient):
        """Test error with invalid file type."""
        file_content = b"text content"
        files = {"file": ("test.txt", BytesIO(file_content), "text/plain")}

        with patch("app.api.process_file_with_fallback") as mock_process:
            mock_process.side_effect = Exception("Invalid file type")
            response = client.post("/internal/v1/parse", files=files)
            assert response.status_code == 500

    def test_file_too_large(self, client: TestClient, large_file_content: bytes):
        """Test error when file is too large."""
        files = {"file": ("large.pdf", BytesIO(large_file_content), "application/pdf")}

        with patch("app.api.process_file_with_fallback") as mock_process:
            mock_process.side_effect = Exception("File too large")
            response = client.post("/internal/v1/parse", files=files)
            assert response.status_code == 500

    def test_multimodal_disabled(self, client: TestClient):
        """Test multimodal parsing when disabled in settings."""
        with patch("app.config.settings.ENABLE_MULTIMODAL", False):
            file_content = b"fake pdf content"
            files = {
                "file": ("test.pdf", BytesIO(file_content), "application/pdf"),
                "multimodal": (None, "true"),
            }

            response = client.post("/internal/v1/parse", files=files)
            assert response.status_code == 403
            assert "Multimodal parsing is disabled" in response.text

    @patch("app.api.cached_cv_parser_workflow")
    def test_parsing_error_in_workflow(self, mock_workflow, client: TestClient):
        """Test handling of parsing errors in workflow."""
        # Setup mock to return error
        mock_workflow.ainvoke = AsyncMock(return_value={"error": "Parsing failed"})

        request_data = {"text": "John Doe\nSoftware Engineer"}
        response = client.post("/internal/v1/parse", json=request_data)

        assert response.status_code == 400
        assert "Parsing failed" in response.text

    @patch("app.api.cached_cv_parser_workflow")
    def test_workflow_returns_no_data(self, mock_workflow, client: TestClient):
        """Test handling when workflow returns no parsed data."""
        # Setup mock to return empty result
        mock_workflow.ainvoke = AsyncMock(return_value={"parsed_data": None})

        request_data = {"text": "John Doe\nSoftware Engineer"}
        response = client.post("/internal/v1/parse", json=request_data)

        assert response.status_code == 500
        assert "Failed to parse CV data" in response.text

    def test_malformed_json(self, client: TestClient):
        """Test handling of malformed JSON."""
        response = client.post(
            "/internal/v1/parse",
            content='{"text": "John Doe", invalid json}',
            headers={"Content-Type": "application/json"},
        )
        assert response.status_code == 422  # Unprocessable Entity

    def test_missing_required_text_field(self, client: TestClient):
        """Test error when required text field is missing."""
        request_data = {"filename": "test.txt"}  # Missing 'text' field
        response = client.post("/internal/v1/parse", json=request_data)
        assert response.status_code == 400  # Our custom handling returns 400

    def test_empty_text_input(self, client: TestClient):
        """Test handling of empty text input."""
        request_data = {"text": ""}

        with patch("app.api.cached_cv_parser_workflow") as mock_workflow:
            mock_workflow.ainvoke = AsyncMock(
                return_value={"error": "No text provided for text mode parsing"}
            )
            response = client.post("/internal/v1/parse", json=request_data)
            assert response.status_code == 400

    def test_very_long_text_input(self, client: TestClient, sample_parsed_cv: CVData):
        """Test handling of very long text input."""
        long_text = "John Doe\nSoftware Engineer\n" * 10000  # Very long text
        request_data = {"text": long_text}

        with patch("app.api.cached_cv_parser_workflow") as mock_workflow:
            mock_workflow.ainvoke = AsyncMock(
                return_value={"parsed_data": sample_parsed_cv}
            )
            response = client.post("/internal/v1/parse", json=request_data)
            assert response.status_code == 200

    def test_special_characters_in_filename(
        self, client: TestClient, sample_parsed_cv: CVData
    ):
        """Test handling of special characters in filename."""
        request_data = {"text": "John Doe", "filename": "résumé-ñoël.txt"}

        with patch("app.api.cached_cv_parser_workflow") as mock_workflow:
            mock_workflow.ainvoke = AsyncMock(
                return_value={"parsed_data": sample_parsed_cv}
            )
            response = client.post("/internal/v1/parse", json=request_data)
            assert response.status_code == 200

    @patch("app.api.cached_cv_parser_workflow")
    @patch("app.api.process_file_with_fallback", new_callable=AsyncMock)
    def test_file_with_empty_content(
        self, mock_process_file_with_fallback, mock_workflow, client: TestClient
    ):
        """Test handling of file with no extractable content."""
        # Setup mock to return fallback to multimodal due to empty content
        mock_process_file_with_fallback.return_value = FallbackResult(
            text=None,
            filename="empty.pdf",
            mode_used="multimodal",
            fallback_triggered=True,
            fallback_reason="Poor content quality: Empty content",
            file_base64="base64content",
        )
        mock_workflow.ainvoke = AsyncMock(
            return_value={
                "error": "Could not extract basic personal information from CV"
            }
        )

        file_content = b"fake pdf content"
        files = {"file": ("empty.pdf", BytesIO(file_content), "application/pdf")}

        response = client.post("/internal/v1/parse", files=files)
        assert response.status_code == 400

    def test_concurrent_requests(self, client: TestClient, sample_parsed_cv: CVData):
        """Test handling of concurrent requests."""
        import threading

        results = []

        def make_request():
            request_data = {"text": "John Doe\nSoftware Engineer"}
            with patch("app.api.cached_cv_parser_workflow") as mock_workflow:
                mock_workflow.ainvoke = AsyncMock(
                    return_value={"parsed_data": sample_parsed_cv}
                )
                response = client.post("/internal/v1/parse", json=request_data)
                results.append(response.status_code)

        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # All requests should succeed
        assert all(status == 200 for status in results)


class TestErrorHandling:
    """Test error handling across the API."""

    def test_http_exception_handler(self, client: TestClient):
        """Test custom HTTP exception handler."""
        # Trigger a known 400 error
        response = client.post("/internal/v1/parse")
        assert response.status_code == 400
        data = response.json()
        assert "error" in data
        assert "status_code" in data
        assert data["status_code"] == 400

    def test_general_exception_handler(self, client: TestClient):
        """Test general exception handler."""
        # Test with a scenario that causes a general exception in the API layer
        with patch("app.api.process_file_with_fallback") as mock_process_file:
            # Mock to raise a RuntimeError that should be caught by general exception handler
            mock_process_file.side_effect = RuntimeError("Unexpected error")

            file_content = b"fake pdf content"
            files = {"file": ("test.pdf", BytesIO(file_content), "application/pdf")}
            response = client.post("/internal/v1/parse", files=files)

            # This RuntimeError should be caught by the general exception handler
            assert response.status_code == 500
            data = response.json()
            assert data["error"] == "Internal server error"
            assert data["status_code"] == 500

    def test_validation_error_handling(self, client: TestClient):
        """Test Pydantic validation error handling."""
        # Send invalid data type
        response = client.post(
            "/internal/v1/parse",
            json={"text": 123},  # text should be string, not int
        )
        assert response.status_code == 400  # Our custom handling returns 400


class TestAuthentication:
    """Test authentication and authorization scenarios."""

    @patch("app.config.settings.OPENAI_API_KEY", "")
    def test_missing_api_key(self, client: TestClient):
        """Test behavior when OpenAI API key is missing."""
        request_data = {"text": "John Doe"}

        with patch("app.api.cached_cv_parser_workflow") as mock_workflow:
            mock_workflow.ainvoke = AsyncMock(
                side_effect=Exception("OpenAI API key not provided")
            )
            response = client.post("/internal/v1/parse", json=request_data)
            assert response.status_code == 500
