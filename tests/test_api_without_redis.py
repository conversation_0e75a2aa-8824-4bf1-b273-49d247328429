"""
Test API functionality when <PERSON><PERSON> is unavailable
"""

from unittest.mock import patch

import pytest
from fastapi.testclient import TestClient

from app.api import app


class TestAPIWithoutRedis:
    """Test API endpoints when Redis is unavailable"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)

    @patch("app.api.initialize_cache")
    def test_startup_continues_with_cache_failure(self, mock_init_cache):
        """Test that app startup continues even if cache initialization fails"""
        # Simulate cache initialization failure
        mock_init_cache.side_effect = Exception("Redis connection failed")

        # Create a new test client to trigger startup
        with TestClient(app) as client:
            # App should start successfully
            response = client.get("/")
            assert response.status_code == 200

            # Root endpoint should work
            data = response.json()
            assert data["message"] == "CV Parser API"
