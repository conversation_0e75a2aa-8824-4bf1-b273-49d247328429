"""Test script to verify multimodal parsing with split API calls"""

import pytest

from app.parser import (
    PersonalInfoEducationAndSkillsParser,
    WorkExperienceAndProjectsParser,
    AdditionalSectionsAndCertificationsParser,
)


@pytest.mark.asyncio
async def test_multimodal_parsers():
    """Test multimodal parsing with individual parsers"""
    # Create a simple test image (1x1 white pixel PNG)
    test_image_base64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
    image_mime_type = "image/png"
    filename = "test_cv.png"

    # Create parser instances
    personal_parser = PersonalInfoEducationAndSkillsParser()
    work_parser = WorkExperienceAndProjectsParser()
    additional_parser = AdditionalSectionsAndCertificationsParser()

    try:
        print("Testing multimodal parsers with split API calls...")

        # Test personal info parser
        print("\n1. Testing PersonalInfoEducationAndSkillsParser...")
        try:
            personal_result = await personal_parser.parse_from_images(
                [test_image_base64], image_mime_type, filename
            )
            print(f"   ✓ Personal info parser returned: {type(personal_result)}")
        except Exception as e:
            print(f"   ✗ Personal info parser failed: {str(e)}")

        # Test work experience parser
        print("\n2. Testing WorkExperienceAndProjectsParser...")
        try:
            work_result = await work_parser.parse_from_images(
                [test_image_base64], image_mime_type, filename
            )
            print(f"   ✓ Work parser returned: {type(work_result)}")
        except Exception as e:
            print(f"   ✗ Work parser failed: {str(e)}")

        # Test additional sections parser
        print("\n3. Testing AdditionalSectionsAndCertificationsParser...")
        try:
            additional_result = await additional_parser.parse_from_images(
                [test_image_base64], image_mime_type, filename
            )
            print(
                f"   ✓ Additional sections parser returned: {type(additional_result)}"
            )
        except Exception as e:
            print(f"   ✗ Additional sections parser failed: {str(e)}")

        print("\n✅ All parsers have parse_from_images method implemented correctly!")

    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        raise
