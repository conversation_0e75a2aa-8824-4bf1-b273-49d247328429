"""Tests for file processing functionality."""

import os
import tempfile
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
from fastapi import HTTPException

from app.file_processors import (
    FileProcessor,
    FileProcessorFactory,
    PDFToImageConverter,
    TextractProcessor,
    get_file_base64,
    process_file,
)


class TestFileProcessor:
    """Test the base FileProcessor class."""

    def test_validate_file_success(self, mock_upload_file):
        """Test successful file validation."""
        # Create a valid file
        content = b"valid pdf content"
        file = mock_upload_file(content, "test.pdf", "application/pdf")

        allowed_extensions = {".pdf", ".doc", ".docx"}
        max_size = 1024 * 1024  # 1MB

        # Should not raise any exception
        FileProcessor.validate_file(file, allowed_extensions, max_size)

    def test_validate_file_invalid_extension(self, mock_upload_file):
        """Test file validation with invalid extension."""
        content = b"text content"
        file = mock_upload_file(content, "test.txt", "text/plain")

        allowed_extensions = {".pdf", ".doc", ".docx"}
        max_size = 1024 * 1024

        with pytest.raises(HTTPException) as exc_info:
            FileProcessor.validate_file(file, allowed_extensions, max_size)

        assert exc_info.value.status_code == 400
        assert "not supported" in str(exc_info.value.detail)

    def test_validate_file_too_large(self, mock_upload_file):
        """Test file validation with file too large."""
        content = b"x" * (2 * 1024 * 1024)  # 2MB content
        file = mock_upload_file(content, "large.pdf", "application/pdf")

        allowed_extensions = {".pdf"}
        max_size = 1024 * 1024  # 1MB limit

        with pytest.raises(HTTPException) as exc_info:
            FileProcessor.validate_file(file, allowed_extensions, max_size)

        assert exc_info.value.status_code == 400
        assert "exceeds maximum allowed size" in str(exc_info.value.detail)

    def test_validate_file_case_insensitive_extension(self, mock_upload_file):
        """Test file validation is case insensitive for extensions."""
        content = b"pdf content"
        file = mock_upload_file(content, "test.PDF", "application/pdf")

        allowed_extensions = {".pdf"}
        max_size = 1024 * 1024

        # Should not raise exception (case insensitive)
        FileProcessor.validate_file(file, allowed_extensions, max_size)

    @pytest.mark.asyncio
    async def test_save_temp_file(self, mock_upload_file):
        """Test saving file to temporary location."""
        content = b"test file content"
        file = mock_upload_file(content, "test.pdf")

        temp_path = await FileProcessor.save_temp_file(file)

        try:
            assert os.path.exists(temp_path)
            assert temp_path.endswith(".pdf")

            with open(temp_path, "rb") as f:
                assert f.read() == content
        finally:
            # Cleanup
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_validate_file_zero_size(self, mock_upload_file):
        """Test validation of zero-size file."""
        content = b""  # Empty file
        file = mock_upload_file(content, "empty.pdf")

        allowed_extensions = {".pdf"}
        max_size = 1024 * 1024

        # Empty file should be valid (size check only for max size)
        FileProcessor.validate_file(file, allowed_extensions, max_size)


class TestTextractProcessor:
    """Test unified textract processor for all file types."""

    @patch("app.file_processors.subprocess.run")
    def test_extract_pdf_success(self, mock_subprocess):
        """Test successful PDF text extraction with pdftotext command."""
        # Mock successful subprocess.run call
        mock_result = Mock()
        mock_result.stdout = "Page 1 content\n\nPage 2 content"
        mock_subprocess.return_value = mock_result

        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
            tmp.write(b"fake pdf content")
            temp_path = tmp.name

        try:
            result = TextractProcessor.extract_text(temp_path, ".pdf")
            assert result == "Page 1 content\n\nPage 2 content"
            # Verify pdftotext was called with correct arguments
            mock_subprocess.assert_called_once_with(
                ["pdftotext", "-layout", temp_path, "-"],
                capture_output=True,
                text=True,
                timeout=30,
                check=True,
            )
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.textract.process")
    def test_extract_doc_success(self, mock_textract):
        """Test successful DOC text extraction."""
        mock_textract.return_value = b"Document content from textract"

        with tempfile.NamedTemporaryFile(suffix=".doc", delete=False) as tmp:
            tmp.write(b"fake doc content")
            temp_path = tmp.name

        try:
            result = TextractProcessor.extract_text(temp_path, ".doc")
            assert result == "Document content from textract"
            # Verify DOC was processed without PDF-specific options
            mock_textract.assert_called_once_with(temp_path, encoding="utf-8")
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.textract.process")
    def test_extract_docx_success(self, mock_textract):
        """Test successful DOCX text extraction."""
        mock_textract.return_value = b"DOCX content from textract"

        with tempfile.NamedTemporaryFile(suffix=".docx", delete=False) as tmp:
            tmp.write(b"fake docx content")
            temp_path = tmp.name

        try:
            result = TextractProcessor.extract_text(temp_path, ".docx")
            assert result == "DOCX content from textract"
            # Verify DOCX was processed without PDF-specific options
            mock_textract.assert_called_once_with(temp_path, encoding="utf-8")
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.subprocess.run")
    def test_extract_text_empty_pdf_file(self, mock_subprocess):
        """Test PDF file with no extractable text."""
        # Mock subprocess.run to return only whitespace
        mock_result = Mock()
        mock_result.stdout = "   "  # Only whitespace
        mock_subprocess.return_value = mock_result

        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
            tmp.write(b"fake pdf content")
            temp_path = tmp.name

        try:
            with pytest.raises(ValueError) as exc_info:
                TextractProcessor.extract_text(temp_path, ".pdf")

            assert "No text content found in .PDF file" in str(exc_info.value)
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.textract.process")
    def test_extract_text_empty_doc_file(self, mock_textract):
        """Test DOC file with no extractable text."""
        mock_textract.return_value = b"   "  # Only whitespace

        with tempfile.NamedTemporaryFile(suffix=".doc", delete=False) as tmp:
            tmp.write(b"fake doc content")
            temp_path = tmp.name

        try:
            with pytest.raises(ValueError) as exc_info:
                TextractProcessor.extract_text(temp_path, ".doc")

            assert "No text content found in .DOC file" in str(exc_info.value)
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.subprocess.run")
    def test_extract_pdf_timeout_error(self, mock_subprocess):
        """Test handling of pdftotext timeout."""
        import subprocess

        mock_subprocess.side_effect = subprocess.TimeoutExpired(["pdftotext"], 30)

        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
            tmp.write(b"fake pdf content")
            temp_path = tmp.name

        try:
            with pytest.raises(ValueError) as exc_info:
                TextractProcessor.extract_text(temp_path, ".pdf")
            assert "No text content found in .PDF file" in str(exc_info.value)
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.subprocess.run")
    def test_extract_pdf_command_error(self, mock_subprocess):
        """Test handling of pdftotext command errors."""
        import subprocess

        mock_subprocess.side_effect = subprocess.CalledProcessError(1, ["pdftotext"])

        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
            tmp.write(b"fake pdf content")
            temp_path = tmp.name

        try:
            with pytest.raises(ValueError) as exc_info:
                TextractProcessor.extract_text(temp_path, ".pdf")
            assert "No text content found in .PDF file" in str(exc_info.value)
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.subprocess.run")
    def test_extract_pdf_command_not_found(self, mock_subprocess):
        """Test handling when pdftotext command is not found."""
        mock_subprocess.side_effect = FileNotFoundError("pdftotext command not found")

        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
            tmp.write(b"fake pdf content")
            temp_path = tmp.name

        try:
            with pytest.raises(HTTPException) as exc_info:
                TextractProcessor.extract_text(temp_path, ".pdf")

            assert exc_info.value.status_code == 500
            assert "poppler-utils" in str(exc_info.value.detail)
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.textract.process")
    def test_extract_text_doc_success_after_refactor(self, mock_textract):
        """Test successful DOC extraction after the pdftotext refactor."""
        # Mock successful call that returns bytes with content
        mock_textract.return_value = b"Content extracted successfully"

        with tempfile.NamedTemporaryFile(suffix=".doc", delete=False) as tmp:
            tmp.write(b"fake doc content")
            temp_path = tmp.name

        try:
            result = TextractProcessor.extract_text(temp_path, ".doc")
            assert result == "Content extracted successfully"
            mock_textract.assert_called_once_with(temp_path, encoding="utf-8")
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.textract.process")
    def test_extract_text_textract_error(self, mock_textract):
        """Test handling of textract processing errors."""
        mock_textract.side_effect = Exception("Unsupported format")

        with tempfile.NamedTemporaryFile(suffix=".doc", delete=False) as tmp:
            tmp.write(b"fake doc content")
            temp_path = tmp.name

        try:
            with pytest.raises(HTTPException) as exc_info:
                TextractProcessor.extract_text(temp_path, ".doc")

            assert exc_info.value.status_code == 400
            assert "not supported" in str(exc_info.value.detail)
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.textract.process")
    def test_extract_text_corrupted_doc_file(self, mock_textract):
        """Test handling of corrupted DOC files."""
        mock_textract.side_effect = Exception("File is corrupted")

        with tempfile.NamedTemporaryFile(suffix=".doc", delete=False) as tmp:
            tmp.write(b"corrupted content")
            temp_path = tmp.name

        try:
            with pytest.raises(HTTPException) as exc_info:
                TextractProcessor.extract_text(temp_path, ".doc")

            assert exc_info.value.status_code == 400
            assert "corrupted or invalid" in str(exc_info.value.detail)
        finally:
            os.unlink(temp_path)

    @patch("app.file_processors.textract.process")
    def test_extract_text_password_protected_doc(self, mock_textract):
        """Test handling of password-protected DOC files."""
        mock_textract.side_effect = Exception("Password required")

        with tempfile.NamedTemporaryFile(suffix=".doc", delete=False) as tmp:
            tmp.write(b"encrypted content")
            temp_path = tmp.name

        try:
            with pytest.raises(HTTPException) as exc_info:
                TextractProcessor.extract_text(temp_path, ".doc")

            assert exc_info.value.status_code == 400
            assert "Password-protected" in str(exc_info.value.detail)
        finally:
            os.unlink(temp_path)


class TestFileProcessorFactory:
    """Test the FileProcessorFactory."""

    def test_get_processor_pdf(self):
        """Test getting processor for PDF."""
        processor = FileProcessorFactory.get_processor(".pdf")
        assert isinstance(processor, TextractProcessor)

    def test_get_processor_doc(self):
        """Test getting processor for DOC."""
        processor = FileProcessorFactory.get_processor(".doc")
        assert isinstance(processor, TextractProcessor)

    def test_get_processor_docx(self):
        """Test getting processor for DOCX."""
        processor = FileProcessorFactory.get_processor(".docx")
        assert isinstance(processor, TextractProcessor)

    def test_get_processor_case_insensitive(self):
        """Test processor factory is case insensitive."""
        processor = FileProcessorFactory.get_processor(".PDF")
        assert isinstance(processor, TextractProcessor)

    def test_get_processor_unsupported_type(self):
        """Test error for unsupported file type."""
        with pytest.raises(HTTPException) as exc_info:
            FileProcessorFactory.get_processor(".txt")

        assert exc_info.value.status_code == 400
        assert "No processor available" in str(exc_info.value.detail)


class TestProcessFile:
    """Test the main process_file function."""

    @pytest.mark.asyncio
    @patch("app.file_processors.FileProcessorFactory.get_processor")
    async def test_process_file_success(self, mock_get_processor, mock_upload_file):
        """Test successful file processing."""
        # Setup mocks
        mock_processor = Mock()
        mock_processor.extract_text_async = AsyncMock(
            return_value="Extracted text content"
        )
        mock_get_processor.return_value = mock_processor

        content = b"fake pdf content"
        file = mock_upload_file(content, "test.pdf")
        allowed_extensions = {".pdf"}
        max_size = 1024 * 1024

        text, filename = await process_file(file, allowed_extensions, max_size)

        assert text == "Extracted text content"
        assert filename == "test.pdf"
        # Verify extract_text_async was called with correct arguments
        args = mock_processor.extract_text_async.call_args[0]
        assert args[1] == ".pdf"  # Second argument is file extension

    @pytest.mark.asyncio
    async def test_process_file_validation_error(self, mock_upload_file):
        """Test process_file with validation error."""
        content = b"text content"
        file = mock_upload_file(content, "test.txt")  # Invalid extension
        allowed_extensions = {".pdf"}
        max_size = 1024 * 1024

        with pytest.raises(HTTPException):
            await process_file(file, allowed_extensions, max_size)

    @pytest.mark.asyncio
    @patch("app.file_processors.FileProcessorFactory.get_processor")
    async def test_process_file_extraction_error(
        self, mock_get_processor, mock_upload_file
    ):
        """Test process_file with text extraction error."""
        # Setup mock to raise error
        mock_processor = Mock()
        mock_processor.extract_text_async = AsyncMock(
            side_effect=Exception("Extraction failed")
        )
        mock_get_processor.return_value = mock_processor

        content = b"fake pdf content"
        file = mock_upload_file(content, "test.pdf")
        allowed_extensions = {".pdf"}
        max_size = 1024 * 1024

        with pytest.raises(Exception):
            await process_file(file, allowed_extensions, max_size)

    @pytest.mark.asyncio
    @patch("app.file_processors.FileProcessorFactory.get_processor")
    @patch("os.path.exists")
    @patch("os.unlink")
    async def test_process_file_cleanup_on_error(
        self, mock_unlink, mock_exists, mock_get_processor, mock_upload_file
    ):
        """Test that temporary files are cleaned up on error."""
        # Setup mocks
        mock_exists.return_value = True
        mock_processor = Mock()
        mock_processor.extract_text_async = AsyncMock(
            side_effect=Exception("Extraction failed")
        )
        mock_get_processor.return_value = mock_processor

        content = b"fake pdf content"
        file = mock_upload_file(content, "test.pdf")
        allowed_extensions = {".pdf"}
        max_size = 1024 * 1024

        with pytest.raises(Exception):
            await process_file(file, allowed_extensions, max_size)

        # Verify cleanup was called
        mock_unlink.assert_called_once()


class TestPDFToImageConverter:
    """Test the PDFToImageConverter class."""

    @pytest.mark.asyncio
    @patch("app.file_processors.convert_from_bytes")
    @patch("app.file_processors.settings")
    async def test_convert_pdf_to_images_success(
        self, mock_settings, mock_convert_from_bytes
    ):
        """Test successful PDF to images conversion."""
        # Mock settings
        mock_settings.PDF_TO_IMAGE_DPI = 200
        mock_settings.PDF_MAX_PAGES = 10
        mock_settings.PDF_IMAGE_FORMAT = "PNG"

        # Create a mock PIL Image
        mock_image = Mock()
        mock_image.save = Mock()

        # Mock convert_from_bytes to return list of images
        mock_convert_from_bytes.return_value = [mock_image, mock_image]

        # Mock the BytesIO buffer and base64 encoding
        with (
            patch("app.file_processors.io.BytesIO") as mock_bytesio,
            patch("app.file_processors.base64.b64encode") as mock_b64encode,
        ):
            mock_buffer = Mock()
            mock_bytesio.return_value = mock_buffer
            mock_buffer.getvalue.return_value = b"mock_image_bytes"
            mock_b64encode.return_value = (
                b"bW9ja19pbWFnZV9kYXRh"  # base64 for "mock_image_data"
            )

            pdf_bytes = b"fake pdf content"
            result = await PDFToImageConverter.convert_pdf_to_images(
                pdf_bytes, "test.pdf"
            )

            # Verify the result
            assert len(result) == 2
            assert all(isinstance(img, str) for img in result)
            assert result == ["bW9ja19pbWFnZV9kYXRh", "bW9ja19pbWFnZV9kYXRh"]

            # Verify convert_from_bytes was called with correct parameters
            mock_convert_from_bytes.assert_called_once_with(
                pdf_bytes, dpi=200, first_page=1, last_page=10, fmt="png"
            )

    @pytest.mark.asyncio
    @patch("app.file_processors.convert_from_bytes")
    async def test_convert_pdf_to_images_empty_pdf(self, mock_convert_from_bytes):
        """Test conversion of PDF with no pages."""
        mock_convert_from_bytes.return_value = []

        pdf_bytes = b"empty pdf"

        with pytest.raises(HTTPException) as exc_info:
            await PDFToImageConverter.convert_pdf_to_images(pdf_bytes, "empty.pdf")

        assert exc_info.value.status_code == 500
        assert "Error converting PDF to images" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("app.file_processors.convert_from_bytes")
    async def test_convert_pdf_to_images_poppler_error(self, mock_convert_from_bytes):
        """Test handling of poppler-related errors."""
        mock_convert_from_bytes.side_effect = Exception("poppler not found")

        pdf_bytes = b"fake pdf"

        with pytest.raises(HTTPException) as exc_info:
            await PDFToImageConverter.convert_pdf_to_images(pdf_bytes, "test.pdf")

        assert exc_info.value.status_code == 500
        assert "poppler-utils" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("app.file_processors.convert_from_bytes")
    async def test_convert_pdf_to_images_corrupted_pdf(self, mock_convert_from_bytes):
        """Test handling of corrupted PDF files."""
        mock_convert_from_bytes.side_effect = Exception("PDF is corrupted")

        pdf_bytes = b"corrupted pdf"

        with pytest.raises(HTTPException) as exc_info:
            await PDFToImageConverter.convert_pdf_to_images(pdf_bytes, "corrupted.pdf")

        assert exc_info.value.status_code == 400
        assert "Invalid or corrupted PDF file" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    @patch("app.file_processors.convert_from_bytes")
    async def test_convert_pdf_to_images_encrypted_pdf(self, mock_convert_from_bytes):
        """Test handling of password-protected PDF files."""
        mock_convert_from_bytes.side_effect = Exception("PDF is password protected")

        pdf_bytes = b"encrypted pdf"

        with pytest.raises(HTTPException) as exc_info:
            await PDFToImageConverter.convert_pdf_to_images(pdf_bytes, "encrypted.pdf")

        assert exc_info.value.status_code == 400
        assert "Password-protected PDF files are not supported" in str(
            exc_info.value.detail
        )

    @pytest.mark.asyncio
    @patch("app.file_processors.convert_from_bytes")
    @patch("app.file_processors.settings")
    async def test_convert_pdf_to_images_jpeg_format(
        self, mock_settings, mock_convert_from_bytes
    ):
        """Test PDF to images conversion with JPEG format."""
        # Mock settings for JPEG
        mock_settings.PDF_TO_IMAGE_DPI = 150
        mock_settings.PDF_MAX_PAGES = 5
        mock_settings.PDF_IMAGE_FORMAT = "JPEG"

        # Create a mock PIL Image
        mock_image = Mock()
        mock_image.save = Mock()

        mock_convert_from_bytes.return_value = [mock_image]

        with (
            patch("app.file_processors.io.BytesIO") as mock_bytesio,
            patch("app.file_processors.base64.b64encode") as mock_b64encode,
        ):
            mock_buffer = Mock()
            mock_bytesio.return_value = mock_buffer
            mock_buffer.getvalue.return_value = b"jpeg_image_bytes"
            mock_b64encode.return_value = b"anBlZ19pbWFnZV9kYXRh"

            pdf_bytes = b"pdf content"
            result = await PDFToImageConverter.convert_pdf_to_images(
                pdf_bytes, "test.pdf"
            )

            # Verify image.save was called with JPEG format and quality
            mock_image.save.assert_called_once_with(
                mock_buffer, format="JPEG", quality=90
            )

            assert len(result) == 1
            assert result[0] == "anBlZ19pbWFnZV9kYXRh"

    @pytest.mark.asyncio
    @patch("app.file_processors.convert_from_bytes")
    @patch("app.file_processors.settings")
    async def test_convert_pdf_to_images_invalid_format_fallback(
        self, mock_settings, mock_convert_from_bytes
    ):
        """Test fallback to PNG for invalid image format."""
        # Mock settings with invalid format
        mock_settings.PDF_TO_IMAGE_DPI = 200
        mock_settings.PDF_MAX_PAGES = 10
        mock_settings.PDF_IMAGE_FORMAT = "INVALID"

        mock_image = Mock()
        mock_image.save = Mock()
        mock_convert_from_bytes.return_value = [mock_image]

        with (
            patch("app.file_processors.io.BytesIO") as mock_bytesio,
            patch("app.file_processors.base64.b64encode") as mock_b64encode,
        ):
            mock_buffer = Mock()
            mock_bytesio.return_value = mock_buffer
            mock_buffer.getvalue.return_value = b"fallback_image"
            mock_b64encode.return_value = b"ZmFsbGJhY2tfaW1hZ2U="

            pdf_bytes = b"pdf content"
            await PDFToImageConverter.convert_pdf_to_images(pdf_bytes, "test.pdf")

            # Verify image.save was called with PNG format (fallback)
            mock_image.save.assert_called_once_with(
                mock_buffer, format="PNG", quality=None
            )


class TestGetFileBase64:
    """Test the get_file_base64 function."""

    @pytest.mark.asyncio
    async def test_get_file_base64_pdf(self, mock_upload_file):
        """Test base64 encoding for PDF file."""
        content = b"fake pdf content"
        file = mock_upload_file(content, "test.pdf")

        base64_content, mime_type = await get_file_base64(file)

        assert mime_type == "application/pdf"
        # Verify base64 content can be decoded back
        import base64

        decoded = base64.b64decode(base64_content)
        assert decoded == content

    @pytest.mark.asyncio
    async def test_get_file_base64_docx(self, mock_upload_file):
        """Test base64 encoding for DOCX file."""
        content = b"fake docx content"
        file = mock_upload_file(content, "test.docx")

        base64_content, mime_type = await get_file_base64(file)

        assert (
            mime_type
            == "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
        import base64

        decoded = base64.b64decode(base64_content)
        assert decoded == content

    @pytest.mark.asyncio
    async def test_get_file_base64_doc(self, mock_upload_file):
        """Test base64 encoding for DOC file."""
        content = b"fake doc content"
        file = mock_upload_file(content, "test.doc")

        base64_content, mime_type = await get_file_base64(file)

        assert mime_type == "application/msword"
        import base64

        decoded = base64.b64decode(base64_content)
        assert decoded == content

    @pytest.mark.asyncio
    async def test_get_file_base64_unknown_type(self, mock_upload_file):
        """Test base64 encoding for unknown file type."""
        content = b"unknown content"
        file = mock_upload_file(content, "test.xyz")

        base64_content, mime_type = await get_file_base64(file)

        assert mime_type == "application/octet-stream"
        import base64

        decoded = base64.b64decode(base64_content)
        assert decoded == content

    @pytest.mark.asyncio
    async def test_get_file_base64_empty_file(self, mock_upload_file):
        """Test base64 encoding for empty file."""
        content = b""
        file = mock_upload_file(content, "empty.pdf")

        base64_content, mime_type = await get_file_base64(file)

        assert mime_type == "application/pdf"
        assert base64_content == ""  # Empty content -> empty base64

    @pytest.mark.asyncio
    async def test_get_file_base64_file_pointer_reset(self, mock_upload_file):
        """Test that file pointer is reset after reading."""
        content = b"test content"
        file = mock_upload_file(content, "test.pdf")

        # Read file once
        await get_file_base64(file)

        # Read again to verify pointer was reset
        base64_content, mime_type = await get_file_base64(file)

        import base64

        decoded = base64.b64decode(base64_content)
        assert decoded == content  # Should still be able to read full content
