"""Integration tests for the CV Parser API."""

from io import Bytes<PERSON>
from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient

from app.api import app
from app.file_processors import FallbackResult
from app.models import CVData, PersonalInfo


class TestEndToEndIntegration:
    """End-to-end integration tests."""

    @pytest.fixture
    def integration_client(self):
        """Test client for integration tests."""
        return TestClient(app)

    @pytest.mark.asyncio
    @patch("app.api.cached_cv_parser_workflow")
    @patch("app.api.process_file_with_fallback")
    async def test_complete_file_upload_pipeline(
        self,
        mock_process_file_with_fallback,
        mock_workflow,
        integration_client,
        sample_parsed_cv,
    ):
        """Test complete file upload and parsing pipeline."""
        # Setup mocks
        mock_process_file_with_fallback.return_value = FallbackResult(
            text="Sample CV text content",
            filename="test_resume.pdf",
            mode_used="text",
            fallback_triggered=False,
            fallback_reason=None,
            file_base64=None,
        )
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Create test file
        file_content = b"PDF content here"
        files = {"file": ("test_resume.pdf", BytesIO(file_content), "application/pdf")}

        # Make request
        response = integration_client.post("/internal/v1/parse", files=files)

        # Verify response
        assert response.status_code == 200
        data = response.json()

        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data

        # Check profile contains the parsed CV data
        profile = data["profile"]
        assert profile["personal_info"]["full_name"] == "John Doe"
        assert profile["personal_info"]["email"] == "<EMAIL>"

    @pytest.mark.asyncio
    @patch("app.api.cached_cv_parser_workflow")
    async def test_complete_text_parsing_pipeline(
        self, mock_workflow, integration_client, sample_parsed_cv, sample_cv_text
    ):
        """Test complete text parsing pipeline."""
        # Setup mock
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Make request
        response = integration_client.post(
            "/internal/v1/parse",
            json={"text": sample_cv_text, "filename": "resume.txt"},
        )

        # Verify response
        assert response.status_code == 200
        data = response.json()

        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data

        # Check profile contains the parsed CV data
        profile = data["profile"]
        assert profile["personal_info"]["full_name"] == "John Doe"

    @pytest.mark.asyncio
    @patch("app.api.cached_cv_parser_workflow")
    @patch("app.api.process_file_with_fallback")
    async def test_complete_multimodal_pipeline(
        self,
        mock_process_file_with_fallback,
        mock_workflow,
        integration_client,
        sample_parsed_cv,
    ):
        """Test complete multimodal parsing pipeline."""
        # Setup mocks for forced multimodal
        mock_process_file_with_fallback.return_value = FallbackResult(
            text=None,
            filename="multimodal_test.pdf",
            mode_used="multimodal",
            fallback_triggered=False,
            fallback_reason="Forced multimodal mode",
            file_base64="base64_content",
        )
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Create test file
        file_content = b"PDF content for multimodal"
        files = {
            "file": ("multimodal_test.pdf", BytesIO(file_content), "application/pdf"),
            "multimodal": (None, "true"),
        }

        # Make request
        response = integration_client.post("/internal/v1/parse", files=files)

        # Verify response
        assert response.status_code == 200
        data = response.json()

        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data

        # Check profile contains the parsed CV data
        profile = data["profile"]
        assert profile["personal_info"]["full_name"] == "John Doe"

        mock_process_file_with_fallback.assert_called_once()


class TestErrorScenarioIntegration:
    """Integration tests for error scenarios."""

    @pytest.fixture
    def integration_client(self):
        """Test client for integration tests."""
        return TestClient(app)

    def test_file_too_large_integration(self, integration_client):
        """Test file size validation integration."""
        # Create large file content
        large_content = b"x" * (15 * 1024 * 1024)  # 15MB (over limit)
        files = {"file": ("large_file.pdf", BytesIO(large_content), "application/pdf")}

        with patch("app.api.process_file_with_fallback") as mock_process:
            mock_process.side_effect = Exception("File too large")
            response = integration_client.post("/internal/v1/parse", files=files)

        assert response.status_code == 500

    def test_invalid_file_type_integration(self, integration_client):
        """Test invalid file type integration."""
        file_content = b"This is not a PDF"
        files = {"file": ("document.txt", BytesIO(file_content), "text/plain")}

        with patch("app.api.process_file_with_fallback") as mock_process:
            mock_process.side_effect = Exception("Invalid file type")
            response = integration_client.post("/internal/v1/parse", files=files)

        assert response.status_code == 500

    @patch("app.api.cached_cv_parser_workflow")
    def test_parsing_failure_integration(self, mock_workflow, integration_client):
        """Test parsing failure integration."""
        mock_workflow.ainvoke = AsyncMock(return_value={"error": "Failed to parse CV"})

        response = integration_client.post(
            "/internal/v1/parse",
            json={"text": "Invalid CV content", "filename": "test.txt"},
        )

        assert response.status_code == 400
        assert "Failed to parse CV" in response.text

    def test_malformed_request_integration(self, integration_client):
        """Test malformed request integration."""
        # Send invalid JSON
        response = integration_client.post(
            "/internal/v1/parse",
            content='{"text": "valid", "invalid_json": }',
            headers={"Content-Type": "application/json"},
        )

        assert response.status_code == 422

    def test_no_input_integration(self, integration_client):
        """Test no input provided integration."""
        response = integration_client.post("/internal/v1/parse")

        assert response.status_code == 400
        assert (
            "Either file upload or text in request body must be provided"
            in response.text
        )


class TestPerformanceIntegration:
    """Performance-related integration tests."""

    @pytest.fixture
    def integration_client(self):
        """Test client for integration tests."""
        return TestClient(app)

    @patch("app.api.cached_cv_parser_workflow")
    def test_large_text_performance(
        self, mock_workflow, integration_client, sample_parsed_cv, benchmark_data
    ):
        """Test performance with large text input."""
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Test with large CV text
        large_text = benchmark_data["large_cv"]
        response = integration_client.post(
            "/internal/v1/parse", json={"text": large_text, "filename": "large_cv.txt"}
        )

        assert response.status_code == 200
        data = response.json()

        # Check V1-compatible response structure
        assert "request_id" in data
        assert data["status"] == "succeeded"
        assert data["success"] is True
        assert "profile" in data
        assert "meta_data" in data

        # Check profile contains CV data
        profile = data["profile"]
        assert "personal_info" in profile

    @patch("app.api.cached_cv_parser_workflow")
    @patch("app.api.process_file_with_fallback")
    def test_concurrent_requests_performance(
        self,
        mock_process_file_with_fallback,
        mock_workflow,
        integration_client,
        sample_parsed_cv,
    ):
        """Test performance with concurrent requests."""
        import threading
        import time

        # Setup mocks
        mock_process_file_with_fallback.return_value = FallbackResult(
            text="CV text",
            filename="test.pdf",
            mode_used="text",
            fallback_triggered=False,
            fallback_reason=None,
            file_base64=None,
        )
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        results = []
        start_time = time.time()

        def make_request():
            response = integration_client.post(
                "/internal/v1/parse",
                json={"text": "John Doe\nSoftware Engineer", "filename": "test.txt"},
            )
            results.append((response.status_code, time.time() - start_time))

        # Create and start threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # Wait for completion
        for thread in threads:
            thread.join()

        # Verify all requests succeeded
        assert all(status == 200 for status, _ in results)
        # Verify reasonable response times (should complete within 10 seconds)
        assert all(duration < 10 for _, duration in results)


class TestRealFileIntegration:
    """Integration tests with real file processing (when possible)."""

    @pytest.fixture
    def integration_client(self):
        """Test client for integration tests."""
        return TestClient(app)

    def create_simple_pdf(self, content: str) -> bytes:
        """Create a simple PDF for testing."""
        # This is a minimal PDF structure - in real tests you might use pypdf or similar
        pdf_content = f"""
        %PDF-1.4
        1 0 obj
        <<
        /Type /Catalog
        /Pages 2 0 R
        >>
        endobj
        2 0 obj
        <<
        /Type /Pages
        /Kids [3 0 R]
        /Count 1
        >>
        endobj
        3 0 obj
        <<
        /Type /Page
        /Parent 2 0 R
        /MediaBox [0 0 612 792]
        /Resources <<
        /Font <<
        /F1 4 0 R
        >>
        >>
        /Contents 5 0 R
        >>
        endobj
        4 0 obj
        <<
        /Type /Font
        /Subtype /Type1
        /BaseFont /Times-Roman
        >>
        endobj
        5 0 obj
        <<
        /Length {len(content)}
        >>
        stream
        BT
        /F1 12 Tf
        100 700 Td
        ({content}) Tj
        ET
        endstream
        endobj
        xref
        0 6
        0000000000 65535 f
        0000000009 00000 n
        0000000058 00000 n
        0000000115 00000 n
        0000000274 00000 n
        0000000354 00000 n
        trailer
        <<
        /Size 6
        /Root 1 0 R
        >>
        startxref
        {400 + len(content)}
        %%EOF
        """
        return pdf_content.encode("latin-1")

    @patch("app.api.cached_cv_parser_workflow")
    def test_pdf_file_processing_integration(
        self, mock_workflow, integration_client, sample_parsed_cv
    ):
        """Test PDF file processing integration with mock workflow."""
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Create a simple PDF
        pdf_content = self.create_simple_pdf("John Doe Software Engineer")

        # Mock the PDF processing since we don't have real PDF parsing in test
        with patch("app.api.process_file_with_fallback") as mock_process:
            mock_process.return_value = FallbackResult(
                text="John Doe\nSoftware Engineer",
                filename="test.pdf",
                mode_used="text",
                fallback_triggered=False,
                fallback_reason=None,
                file_base64=None,
            )

            files = {
                "file": ("test_resume.pdf", BytesIO(pdf_content), "application/pdf")
            }
            response = integration_client.post("/internal/v1/parse", files=files)

            assert response.status_code == 200
            data = response.json()

            # Check V1-compatible response structure
            assert "request_id" in data
            assert data["status"] == "succeeded"
            assert data["success"] is True
            assert "profile" in data
            assert "meta_data" in data

            # Check profile contains the parsed CV data
            profile = data["profile"]
            assert profile["personal_info"]["full_name"] == "John Doe"


class TestAPIBehaviorIntegration:
    """Integration tests for API behavior and edge cases."""

    @pytest.fixture
    def integration_client(self):
        """Test client for integration tests."""
        return TestClient(app)

    def test_cors_headers_integration(self, integration_client):
        """Test CORS headers are properly set."""
        response = integration_client.options("/internal/v1/parse")
        # FastAPI test client doesn't automatically include CORS headers in OPTIONS
        # but we can test that the middleware is configured
        assert response.status_code in [
            200,
            405,
        ]  # Either allowed or method not allowed

    def test_content_type_handling_integration(self, integration_client):
        """Test various content types."""
        # Test JSON content type
        response = integration_client.post(
            "/internal/v1/parse",
            json={"text": "John Doe"},
            headers={"Content-Type": "application/json"},
        )
        # Should work or return a parsing error, not a content-type error
        assert response.status_code in [200, 400, 500]

    def test_response_format_consistency(self, integration_client, sample_parsed_cv):
        """Test response format consistency across different endpoints."""
        with patch("app.api.cached_cv_parser_workflow") as mock_workflow:
            mock_workflow.ainvoke = AsyncMock(
                return_value={"parsed_data": sample_parsed_cv}
            )

            # Test text parsing response format
            text_response = integration_client.post(
                "/internal/v1/parse", json={"text": "John Doe\nSoftware Engineer"}
            )

            # Test file upload response format (mocked)
            with patch("app.api.process_file_with_fallback") as mock_process:
                mock_process.return_value = FallbackResult(
                    text="John Doe\nSoftware Engineer",
                    filename="test.pdf",
                    mode_used="text",
                    fallback_triggered=False,
                    fallback_reason=None,
                    file_base64=None,
                )
                file_content = b"fake pdf"
                files = {"file": ("test.pdf", BytesIO(file_content), "application/pdf")}
                file_response = integration_client.post(
                    "/internal/v1/parse", files=files
                )

            # Both should have same structure
            if text_response.status_code == 200 and file_response.status_code == 200:
                text_data = text_response.json()
                file_data = file_response.json()

                # Check same top-level keys (V1-compatible structure)
                assert set(text_data.keys()) == set(file_data.keys())
                assert "request_id" in text_data
                assert "status" in text_data
                assert "success" in text_data
                assert "profile" in text_data
                assert "meta_data" in text_data

                # Check profile structure is consistent
                assert "personal_info" in text_data["profile"]
                assert "personal_info" in file_data["profile"]

    def test_health_endpoint_availability(self, integration_client):
        """Test health endpoint is always available."""
        response = integration_client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "version" in data
        assert "model" in data

    def test_root_endpoint_information(self, integration_client):
        """Test root endpoint provides correct API information."""
        response = integration_client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "CV Parser API"
        assert "version" in data
        assert "endpoints" in data
        assert "/internal/v1/parse" in data["endpoints"].values()
        assert "/health" in data["endpoints"].values()


class TestStateManagement:
    """Test state management across requests."""

    @pytest.fixture
    def integration_client(self):
        """Test client for integration tests."""
        return TestClient(app)

    @patch("app.api.cached_cv_parser_workflow")
    def test_stateless_requests(
        self, mock_workflow, integration_client, sample_parsed_cv
    ):
        """Test that requests are stateless."""
        mock_workflow.ainvoke = AsyncMock(
            return_value={"parsed_data": sample_parsed_cv}
        )

        # Make first request
        response1 = integration_client.post(
            "/internal/v1/parse",
            json={"text": "First CV content", "filename": "cv1.txt"},
        )

        # Make second request
        response2 = integration_client.post(
            "/internal/v1/parse",
            json={"text": "Second CV content", "filename": "cv2.txt"},
        )

        # Both should succeed independently
        assert response1.status_code == 200
        assert response2.status_code == 200

        # Verify calls were made independently
        assert mock_workflow.ainvoke.call_count == 2

    def test_no_session_persistence(self, integration_client):
        """Test that no session data persists between requests."""
        # Make request with one client
        response1 = integration_client.get("/health")

        # Make request with same client - should be independent
        response2 = integration_client.get("/health")

        assert response1.status_code == 200
        assert response2.status_code == 200
        # Each request should be handled independently


class TestConfigurationIntegration:
    """Test configuration-dependent behavior."""

    @pytest.fixture
    def integration_client(self):
        """Test client for integration tests."""
        return TestClient(app)

    def test_multimodal_disabled_integration(self, integration_client):
        """Test behavior when multimodal is disabled."""
        with patch("app.config.settings.ENABLE_MULTIMODAL", False):
            file_content = b"fake pdf content"
            files = {
                "file": ("test.pdf", BytesIO(file_content), "application/pdf"),
                "multimodal": (None, "true"),
            }

            response = integration_client.post("/internal/v1/parse", files=files)
            assert response.status_code == 403
            assert "Multimodal parsing is disabled" in response.text

    def test_file_size_limit_integration(self, integration_client):
        """Test file size limits are enforced."""
        # Test with content just under limit (should work with proper mocks)
        normal_content = b"x" * (1024 * 1024)  # 1MB

        with patch("app.api.process_file_with_fallback") as mock_process:
            mock_process.return_value = FallbackResult(
                text="Normal CV content",
                filename="normal.pdf",
                mode_used="text",
                fallback_triggered=False,
                fallback_reason=None,
                file_base64=None,
            )

            files = {"file": ("normal.pdf", BytesIO(normal_content), "application/pdf")}

            with patch("app.api.cached_cv_parser_workflow") as mock_workflow:
                mock_workflow.ainvoke = AsyncMock(
                    return_value={
                        "parsed_data": CVData(
                            personal_info=PersonalInfo(full_name="Test")
                        )
                    }
                )
                response = integration_client.post("/internal/v1/parse", files=files)
                # Should succeed (mocked processing)
                assert response.status_code == 200
