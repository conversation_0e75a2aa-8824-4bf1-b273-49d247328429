"""Tests for callback retry functionality"""

import pytest
import httpx
from unittest.mock import AsyncMock, patch, MagicMock
import logging

from app.callback import send_callback, CallbackPayload, _should_retry_callback_error


@pytest.fixture
def sample_payload():
    """Sample callback payload for testing"""
    return CallbackPayload(
        request_id="test-123",
        status="succeeded",
        success=True,
        profile={"name": "<PERSON> Doe"},
        meta_data={"parser_time": "2024-01-01T00:00:00"},
        original_filename="test.pdf",
    )


class TestCallbackRetryLogic:
    """Test the retry logic for callback functions"""

    def test_should_retry_callback_error_with_server_errors(self):
        """Test that server errors (5xx) should trigger retries"""
        # Mock HTTPStatusError with 500 status
        mock_response = MagicMock()
        mock_response.status_code = 500
        error = httpx.HTTPStatusError(
            "Server Error", request=MagicMock(), response=mock_response
        )

        assert _should_retry_callback_error(error) is True

    def test_should_retry_callback_error_with_specific_client_errors(self):
        """Test that specific client errors (408, 429) should trigger retries"""
        for status_code in [408, 429]:
            mock_response = MagicMock()
            mock_response.status_code = status_code
            error = httpx.HTTPStatusError(
                "Client Error", request=MagicMock(), response=mock_response
            )

            assert _should_retry_callback_error(error) is True

    def test_should_not_retry_callback_error_with_other_client_errors(self):
        """Test that other client errors (400, 401, 403, 404) should not trigger retries"""
        for status_code in [400, 401, 403, 404]:
            mock_response = MagicMock()
            mock_response.status_code = status_code
            error = httpx.HTTPStatusError(
                "Client Error", request=MagicMock(), response=mock_response
            )

            assert _should_retry_callback_error(error) is False

    def test_should_retry_callback_error_with_connection_errors(self):
        """Test that connection errors should trigger retries"""
        connection_errors = [
            httpx.ConnectError("Connection failed"),
            httpx.TimeoutException("Timeout"),
            httpx.ReadTimeout("Read timeout"),
            httpx.WriteTimeout("Write timeout"),
            httpx.ConnectTimeout("Connect timeout"),
            httpx.PoolTimeout("Pool timeout"),
        ]

        for error in connection_errors:
            assert _should_retry_callback_error(error) is True

    def test_should_not_retry_other_exceptions(self):
        """Test that other exceptions should not trigger retries"""
        other_errors = [
            ValueError("Invalid value"),
            TypeError("Type error"),
            RuntimeError("Runtime error"),
        ]

        for error in other_errors:
            assert _should_retry_callback_error(error) is False


class TestSendCallback:
    """Test the send_callback function with retry behavior"""

    @pytest.mark.asyncio
    async def test_send_callback_success_on_first_attempt(self, sample_payload):
        """Test successful callback on first attempt"""
        with patch("httpx.AsyncClient") as mock_client:
            mock_response = MagicMock()
            mock_response.raise_for_status.return_value = None

            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                return_value=mock_response
            )

            # Should not raise any exception
            await send_callback("https://example.com/callback", sample_payload)

            # Verify the post was called once
            mock_client.return_value.__aenter__.return_value.post.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_callback_retries_on_server_error(self, sample_payload, caplog):
        """Test that callback retries on server errors"""
        with patch("httpx.AsyncClient") as mock_client:
            # Mock response with 500 error
            mock_response = MagicMock()
            mock_response.status_code = 500
            mock_response.reason_phrase = "Internal Server Error"

            error = httpx.HTTPStatusError(
                "Server Error", request=MagicMock(), response=mock_response
            )
            mock_response.raise_for_status.side_effect = error

            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                return_value=mock_response
            )

            # Should raise the error after retries
            with pytest.raises(httpx.HTTPStatusError):
                await send_callback("https://example.com/callback", sample_payload)

            # Verify multiple attempts were made (3 attempts = 1 initial + 2 retries)
            assert mock_client.return_value.__aenter__.return_value.post.call_count == 3

    @pytest.mark.asyncio
    async def test_send_callback_no_retry_on_non_retryable_error(self, sample_payload):
        """Test that callback doesn't retry on non-retryable client errors"""
        with patch("httpx.AsyncClient") as mock_client:
            # Mock response with 400 error (non-retryable)
            mock_response = MagicMock()
            mock_response.status_code = 400
            mock_response.reason_phrase = "Bad Request"

            error = httpx.HTTPStatusError(
                "Bad Request", request=MagicMock(), response=mock_response
            )
            mock_response.raise_for_status.side_effect = error

            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                return_value=mock_response
            )

            # Should return without raising (non-retryable error handling)
            await send_callback("https://example.com/callback", sample_payload)

            # Verify only one attempt was made
            assert mock_client.return_value.__aenter__.return_value.post.call_count == 1

    @pytest.mark.asyncio
    async def test_send_callback_retries_on_connection_error(self, sample_payload):
        """Test that callback retries on connection errors"""
        with patch("httpx.AsyncClient") as mock_client:
            # Mock connection error
            error = httpx.ConnectError("Connection failed")
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                side_effect=error
            )

            # Should raise the error after retries
            with pytest.raises(httpx.ConnectError):
                await send_callback("https://example.com/callback", sample_payload)

            # Verify multiple attempts were made
            assert mock_client.return_value.__aenter__.return_value.post.call_count == 3

    @pytest.mark.asyncio
    async def test_send_callback_succeeds_after_retry(self, sample_payload):
        """Test that callback succeeds after initial failures"""
        with patch("httpx.AsyncClient") as mock_client:
            # First call fails with 500, second call succeeds
            mock_response_fail = MagicMock()
            mock_response_fail.status_code = 500
            error = httpx.HTTPStatusError(
                "Server Error", request=MagicMock(), response=mock_response_fail
            )
            mock_response_fail.raise_for_status.side_effect = error

            mock_response_success = MagicMock()
            mock_response_success.raise_for_status.return_value = None

            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                side_effect=[mock_response_fail, mock_response_success]
            )

            # Should succeed without raising
            await send_callback("https://example.com/callback", sample_payload)

            # Verify two attempts were made
            assert mock_client.return_value.__aenter__.return_value.post.call_count == 2

    @pytest.mark.asyncio
    async def test_send_callback_logs_retry_attempts(self, sample_payload, caplog):
        """Test that retry attempts are properly logged"""
        with patch("httpx.AsyncClient") as mock_client:
            # Mock server error that will trigger retries
            mock_response = MagicMock()
            mock_response.status_code = 500
            error = httpx.HTTPStatusError(
                "Server Error", request=MagicMock(), response=mock_response
            )
            mock_response.raise_for_status.side_effect = error

            mock_client.return_value.__aenter__.return_value.post = AsyncMock(
                return_value=mock_response
            )

            with caplog.at_level(logging.WARNING):
                with pytest.raises(httpx.HTTPStatusError):
                    await send_callback("https://example.com/callback", sample_payload)

            # Check that retry logs were generated
            retry_logs = [
                record for record in caplog.records if "Retrying" in record.message
            ]
            assert len(retry_logs) >= 2  # Should have at least 2 retry log messages
