"""Test token usage tracking for multimodal API calls"""

import pytest
from unittest.mock import patch, <PERSON><PERSON>, AsyncMock

from app.parser import (
    PersonalInfoEducationAndSkillsParser,
    WorkExperienceAndProjectsParser,
    AdditionalSectionsAndCertificationsParser,
)


class TestTokenUsageTracking:
    """Test token usage tracking functionality"""

    @pytest.mark.asyncio
    async def test_personal_info_parser_token_tracking(self):
        """Test that PersonalInfoParser logs token usage"""
        parser = PersonalInfoEducationAndSkillsParser()

        # Mock the callback and chain
        with patch("app.parser.base.get_openai_callback") as mock_callback:
            mock_cb = Mock()
            mock_cb.prompt_tokens = 1250
            mock_cb.completion_tokens = 425
            mock_cb.total_tokens = 1675
            mock_cb.total_cost = 0.0084
            mock_callback.return_value.__enter__.return_value = mock_cb

            with patch.object(parser, "create_multimodal_chain") as mock_chain:
                mock_chain_instance = Mock()
                mock_chain_instance.ainvoke = AsyncMock(
                    return_value=Mock(
                        personal_info=Mock(phone=None),
                        education=[],
                        skills=[],
                        summary="Test",
                    )
                )
                mock_chain.return_value = mock_chain_instance

                # Mock logger to capture log calls
                with patch("app.parser.base.logger") as mock_logger:
                    await parser.parse_from_images(
                        ["test_image"], "image/png", "test.png"
                    )

                    # Verify token usage was logged
                    # Check that the log was called with token information
                    log_calls = [
                        call.args[0] for call in mock_logger.info.call_args_list
                    ]
                    token_logs = [
                        log
                        for log in log_calls
                        if "Token usage" in log
                        and "PersonalInfoEducationAndSkillsParser" in log
                    ]
                    assert len(token_logs) == 1
                    token_log = token_logs[0]
                    assert "Input: 1,250" in token_log
                    assert "Output: 425" in token_log
                    assert "Total: 1,675" in token_log
                    assert "Cost: $0.0084" in token_log

    @pytest.mark.asyncio
    async def test_work_experience_parser_token_tracking(self):
        """Test that WorkExperienceParser logs token usage"""
        parser = WorkExperienceAndProjectsParser()

        # Mock the callback and chain
        with patch("app.parser.base.get_openai_callback") as mock_callback:
            mock_cb = Mock()
            mock_cb.prompt_tokens = 890
            mock_cb.completion_tokens = 320
            mock_cb.total_tokens = 1210
            mock_cb.total_cost = 0.0061
            mock_callback.return_value.__enter__.return_value = mock_cb

            with patch.object(parser, "create_multimodal_chain") as mock_chain:
                mock_chain_instance = Mock()
                mock_chain_instance.ainvoke = AsyncMock(
                    return_value=Mock(work_experience=[], projects=[])
                )
                mock_chain.return_value = mock_chain_instance

                # Mock logger to capture log calls
                with patch("app.parser.base.logger") as mock_logger:
                    await parser.parse_from_images(
                        ["test_image"], "image/png", "test.png"
                    )

                    # Verify token usage was logged
                    # Check that the log was called with token information
                    log_calls = [
                        call.args[0] for call in mock_logger.info.call_args_list
                    ]
                    token_logs = [
                        log
                        for log in log_calls
                        if "Token usage" in log
                        and "WorkExperienceAndProjectsParser" in log
                    ]
                    assert len(token_logs) == 1
                    token_log = token_logs[0]
                    assert "Input: 890" in token_log
                    assert "Output: 320" in token_log
                    assert "Total: 1,210" in token_log
                    assert "Cost: $0.0061" in token_log

    @pytest.mark.asyncio
    async def test_additional_sections_parser_token_tracking(self):
        """Test that AdditionalSectionsParser logs token usage"""
        parser = AdditionalSectionsAndCertificationsParser()

        # Mock the callback and chain
        with patch("app.parser.base.get_openai_callback") as mock_callback:
            mock_cb = Mock()
            mock_cb.prompt_tokens = 780
            mock_cb.completion_tokens = 180
            mock_cb.total_tokens = 960
            mock_cb.total_cost = 0.0048
            mock_callback.return_value.__enter__.return_value = mock_cb

            with patch.object(parser, "create_multimodal_chain") as mock_chain:
                mock_chain_instance = Mock()
                mock_chain_instance.ainvoke = AsyncMock(
                    return_value=Mock(
                        languages=[],
                        volunteer_experience=[],
                        extracurricular_activities=[],
                        references=[],
                        certifications=[],
                        awards=[],
                        publications=[],
                    )
                )
                mock_chain.return_value = mock_chain_instance

                # Mock logger to capture log calls
                with patch("app.parser.base.logger") as mock_logger:
                    await parser.parse_from_images(
                        ["test_image"], "image/png", "test.png"
                    )

                    # Verify token usage was logged
                    # Check that the log was called with token information
                    log_calls = [
                        call.args[0] for call in mock_logger.info.call_args_list
                    ]
                    token_logs = [
                        log
                        for log in log_calls
                        if "Token usage" in log
                        and "AdditionalSectionsAndCertificationsParser" in log
                    ]
                    assert len(token_logs) == 1
                    token_log = token_logs[0]
                    assert "Input: 780" in token_log
                    assert "Output: 180" in token_log
                    assert "Total: 960" in token_log
                    assert "Cost: $0.0048" in token_log

    def test_token_usage_import(self):
        """Test that get_openai_callback is properly imported"""
        from app.parser import get_openai_callback

        assert get_openai_callback is not None
