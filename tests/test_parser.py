"""Tests for CV parser functionality."""

from unittest.mock import As<PERSON><PERSON><PERSON>, Mock, patch

import pytest

from app.models import (
    Award,
    Certification,
    CVData,
    Education,
    Language,
    PersonalInfo,
    Project,
    Skill,
    WorkExperience,
)
from app.parser import (
    CVParser,
    CVParserState,
    aggregate_results_node,
    create_cv_parser_workflow,
    extract_text_node,
    parse_personal_info_and_education_node,
    validation_node,
)
from app.parser.sections.personal_info import PersonalInfoEducationAndSkills


class TestCVParser:
    """Test the CVParser class."""

    @patch("app.parser.cv_parser.ChatOpenAI")
    def test_cv_parser_initialization(self, mock_chat_openai):
        """Test CVParser initialization."""
        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        assert parser.llm == mock_llm
        assert hasattr(parser, "text_prompt")
        assert hasattr(parser, "multimodal_prompt_text")
        mock_chat_openai.assert_called_once()

    @patch("app.parser.cv_parser.ChatOpenAI")
    def test_create_text_chain(self, mock_chat_openai):
        """Test text chain creation."""
        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()
        chain = parser.create_text_chain()

        assert chain is not None

    @patch("app.parser.cv_parser.ChatOpenAI")
    def test_create_multimodal_chain(self, mock_chat_openai):
        """Test multimodal chain creation."""
        mock_llm = Mock()
        mock_llm.with_structured_output.return_value = "multimodal_chain"
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()
        chain = parser.create_multimodal_chain()

        assert chain == "multimodal_chain"
        mock_llm.with_structured_output.assert_called_once_with(CVData)

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parse_from_text_success(self, mock_chat_openai):
        """Test successful text parsing."""
        # Setup mocks

        mock_chain = AsyncMock()
        parsed_data = CVData(
            personal_info=PersonalInfo(full_name="John Doe", email="<EMAIL>")
        )
        mock_chain.ainvoke.return_value = parsed_data

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        # Mock the chain creation
        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            result = await parser.parse_from_text("Sample CV text", "test.pdf")

        assert result.personal_info.full_name == "John Doe"
        mock_chain.ainvoke.assert_called_once_with("Sample CV text")

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parse_from_text_error(self, mock_chat_openai):
        """Test text parsing with error."""
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Parsing failed")

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_text("Sample CV text", "test.pdf")

        assert "Error parsing CV text" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    @patch("app.parser.cv_parser.HumanMessage")
    @patch("app.file_processors.PDFToImageConverter.convert_pdf_to_images")
    async def test_parse_from_file_success(
        self, mock_convert_pdf, mock_human_message, mock_chat_openai
    ):
        """Test successful multimodal file parsing."""
        # Setup mocks

        # Mock PDF to image conversion
        mock_convert_pdf.return_value = [
            "aW1hZ2UxZGF0YQ==",
            "aW1hZ2UyZGF0YQ==",
        ]  # Valid base64 strings

        mock_chain = AsyncMock()
        parsed_data = CVData(
            personal_info=PersonalInfo(full_name="Jane Doe", email="<EMAIL>")
        )
        mock_chain.ainvoke.return_value = parsed_data

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_multimodal_chain", return_value=mock_chain):
            # Use a valid base64 string
            import base64

            valid_base64 = base64.b64encode(b"fake pdf content").decode("utf-8")
            result = await parser.parse_from_file(
                valid_base64, "application/pdf", "test.pdf"
            )

        assert result.personal_info.full_name == "Jane Doe"
        # Note: metadata field was removed from CVData model
        mock_chain.ainvoke.assert_called_once()
        mock_convert_pdf.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    @patch("app.file_processors.PDFToImageConverter.convert_pdf_to_images")
    async def test_parse_from_file_error(self, mock_convert_pdf, mock_chat_openai):
        """Test multimodal file parsing with error."""
        # Mock PDF to image conversion
        mock_convert_pdf.return_value = ["aW1hZ2UxZGF0YQ=="]

        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Multimodal parsing failed")

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_multimodal_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                # Use a valid base64 string
                import base64

                valid_base64 = base64.b64encode(b"fake pdf content").decode("utf-8")
                await parser.parse_from_file(
                    valid_base64, "application/pdf", "test.pdf"
                )

        assert "Error parsing CV with multimodal" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parse_from_text_empty_response(self, mock_chat_openai):
        """Test parsing with empty/invalid response from LLM."""
        mock_chain = AsyncMock()
        mock_chain.ainvoke.return_value = None

        mock_llm = Mock()
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            result = await parser.parse_from_text("Sample CV text", "test.pdf")
            # The parser should return None if LLM returns None
            assert result is None


class TestWorkflowNodes:
    """Test LangGraph workflow nodes."""

    @pytest.mark.asyncio
    async def test_extract_text_node_text_mode_success(self):
        """Test extract_text_node with valid text mode state."""
        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = await extract_text_node(state)
        assert result == {}  # No error

    @pytest.mark.asyncio
    async def test_extract_text_node_text_mode_no_text(self):
        """Test extract_text_node with text mode but no text."""
        state: CVParserState = {
            "input_text": None,
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = await extract_text_node(state)
        assert "errors" in result
        assert "No text provided for text mode parsing" in result["errors"][0]

    @pytest.mark.asyncio
    async def test_extract_text_node_multimodal_mode_success(self):
        """Test extract_text_node with valid multimodal mode state."""
        state: CVParserState = {
            "input_text": None,
            "input_file_base64": "base64content",
            "filename": "test.pdf",
            "parsed_data": None,
            "errors": [],
            "mode": "multimodal",
        }

        result = await extract_text_node(state)
        assert result == {}  # No error

    @pytest.mark.asyncio
    async def test_extract_text_node_multimodal_mode_no_file(self):
        """Test extract_text_node with multimodal mode but no file data."""
        state: CVParserState = {
            "input_text": None,
            "input_file_base64": None,
            "filename": "test.pdf",
            "parsed_data": None,
            "errors": [],
            "mode": "multimodal",
        }

        result = await extract_text_node(state)
        assert "errors" in result
        assert "No file data provided for multimodal parsing" in result["errors"][0]

    @pytest.mark.asyncio
    @patch("app.parser.workflow.nodes.PersonalInfoEducationAndSkillsParser")
    async def test_parse_personal_info_and_education_node_text_mode_success(
        self, mock_parser_class
    ):
        """Test parse_personal_info_and_education_node with successful text parsing."""
        # Setup mock parser with PersonalInfoEducationAndSkills response (no summary)
        from app.models import Education, Skill
        from app.parser.sections.personal_info import PersonalInfoEducationAndSkills

        personal_info = PersonalInfo(full_name="John Doe", email="<EMAIL>")
        education = [Education(school="University", degree="Bachelor")]
        skills = [Skill(name="Python", category="Technical")]
        combined_result = PersonalInfoEducationAndSkills(
            personal_info=personal_info,
            education=education,
            skills=skills,
        )
        mock_parser = Mock()
        mock_parser.parse_from_text = AsyncMock(return_value=combined_result)
        mock_parser_class.return_value = mock_parser

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = await parse_personal_info_and_education_node(state)

        assert "personal_info_result" in result
        assert "education_result" in result
        assert "skills_result" in result
        # Summary is no longer returned by personal info parser
        assert "summary_result" not in result
        assert result["personal_info_result"].full_name == "John Doe"
        assert len(result["education_result"]) == 1
        assert result["education_result"][0].school == "University"
        mock_parser.parse_from_text.assert_called_once_with("Sample CV text")

    @pytest.mark.asyncio
    @patch("app.parser.workflow.nodes.PersonalInfoEducationAndSkillsParser")
    async def test_parse_personal_info_and_education_node_parsing_error(
        self, mock_parser_class
    ):
        """Test parse_personal_info_and_education_node with parsing error."""
        # Setup mock parser to raise error
        mock_parser = Mock()
        mock_parser.parse_from_text = AsyncMock(side_effect=Exception("Parsing failed"))
        mock_parser_class.return_value = mock_parser

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = await parse_personal_info_and_education_node(state)

        assert "errors" in result
        assert (
            "Personal info, education, and skills parsing failed" in result["errors"][0]
        )

    @pytest.mark.asyncio
    async def test_aggregate_results_node_success(self):
        """Test aggregate_results_node with successful aggregation."""
        from app.models import Education, Skill, WorkExperience

        personal_info = PersonalInfo(full_name="John Doe", email="<EMAIL>")
        education = [Education(school="University", degree="Bachelor")]
        work_experience = [WorkExperience(company="Company", job_title="Developer")]
        skills = [Skill(name="Python", category="Technical")]

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "personal_info_result": personal_info,
            "education_result": education,
            "work_experience_result": work_experience,
            "skills_result": skills,
            "projects_result": [],
            "certifications_result": {},
            "additional_sections_result": {},
        }

        result = await aggregate_results_node(state)

        assert "parsed_data" in result
        parsed_data = result["parsed_data"]
        assert parsed_data.personal_info.full_name == "John Doe"
        assert len(parsed_data.education) == 1
        assert len(parsed_data.work_experience) == 1
        assert len(parsed_data.skills) == 1

    def test_validation_node_success(self):
        """Test validation_node with valid parsed data."""
        parsed_data = CVData(
            personal_info=PersonalInfo(full_name="John Doe", email="<EMAIL>")
        )

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": parsed_data,
            "errors": [],
            "mode": "text",
        }

        result = validation_node(state)
        assert result == {}  # No error

    def test_validation_node_with_existing_error(self):
        """Test validation_node when state already has error."""
        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": ["Previous error"],
            "mode": "text",
        }

        result = validation_node(state)
        assert result == {}  # Returns empty dict when errors exist

    def test_validation_node_no_parsed_data(self):
        """Test validation_node with no parsed data."""
        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
        }

        result = validation_node(state)
        assert "errors" in result
        assert "No parsed data available" in result["errors"][0]

    def test_validation_node_no_personal_info(self):
        """Test validation_node with missing personal info."""
        parsed_data = CVData(summary="Some summary")  # No personal_info

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": parsed_data,
            "errors": [],
            "mode": "text",
        }

        result = validation_node(state)
        assert "errors" in result
        assert "Could not extract basic personal information" in result["errors"][0]

    def test_validation_node_no_full_name(self):
        """Test validation_node with personal info but no full name."""
        parsed_data = CVData(
            personal_info=PersonalInfo(email="<EMAIL>")  # No full_name
        )

        state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": parsed_data,
            "errors": [],
            "mode": "text",
        }

        result = validation_node(state)
        assert "errors" in result
        assert "Could not extract basic personal information" in result["errors"][0]


class TestWorkflowIntegration:
    """Test the complete LangGraph workflow."""

    def test_create_cv_parser_workflow(self):
        """Test workflow creation."""
        workflow = create_cv_parser_workflow()
        assert workflow is not None

    @pytest.mark.asyncio
    @patch("app.parser.workflow.nodes.PersonalInfoEducationAndSkillsParser")
    @patch("app.parser.workflow.nodes.WorkExperienceAndProjectsParser")
    @patch("app.parser.workflow.nodes.AdditionalSectionsAndCertificationsParser")
    async def test_workflow_execution_success(
        self,
        mock_additional,
        mock_skills_work_projects,
        mock_personal_education,
    ):
        """Test complete workflow execution with success."""
        # Setup mock parsers for each section
        from app.models import Education, Skill
        from app.parser.sections.personal_info import PersonalInfoEducationAndSkills

        personal_info = PersonalInfo(full_name="John Doe", email="<EMAIL>")
        education = [Education(school="University", degree="Bachelor")]
        skills = [Skill(name="Python", category="Technical")]
        combined_result = PersonalInfoEducationAndSkills(
            personal_info=personal_info,
            education=education,
            skills=skills,
        )
        mock_personal_education_parser = Mock()
        mock_personal_education_parser.parse_from_text = AsyncMock(
            return_value=combined_result
        )
        mock_personal_education.return_value = mock_personal_education_parser

        # Setup combined skills, work experience, and projects parser
        mock_skills_work_projects_parser = Mock()
        work_experience = [WorkExperience(company="TestCorp", job_title="Developer")]
        skills = [Skill(name="Python", category="Technical")]
        projects = [Project(name="Test Project")]
        mock_skills_work_projects_parser.parse_from_text = AsyncMock(
            return_value=(work_experience, projects)
        )
        mock_skills_work_projects.return_value = mock_skills_work_projects_parser

        # Setup combined additional sections and certifications parser
        mock_additional_parser = Mock()
        mock_additional_parser.parse_from_text = AsyncMock(
            return_value={
                "languages": [],
                "volunteer_experience": [],
                "extracurricular_activities": [],
                "references": [],
                "certifications": [],
                "awards": [],
                "publications": [],
                "summary": "Experienced software engineer with 5+ years in Python development.",
            }
        )
        mock_additional.return_value = mock_additional_parser

        # Import the actual workflow
        from app.parser import cv_parser_workflow

        initial_state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        result = await cv_parser_workflow.ainvoke(initial_state)

        assert "parsed_data" in result
        assert result["parsed_data"].personal_info.full_name == "John Doe"
        assert result.get("error") is None

    @pytest.mark.asyncio
    async def test_workflow_execution_text_validation_error(self):
        """Test workflow execution with text validation error."""
        from app.parser import cv_parser_workflow

        # State with no input text for text mode
        initial_state: CVParserState = {
            "input_text": None,
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        result = await cv_parser_workflow.ainvoke(initial_state)

        assert "errors" in result
        assert "No text provided for text mode parsing" in result["errors"][0]

    @pytest.mark.asyncio
    @patch("app.parser.workflow.nodes.PersonalInfoEducationAndSkillsParser")
    async def test_workflow_execution_parsing_error(
        self, mock_personal_education_parser_class
    ):
        """Test workflow execution with parsing error."""
        # Setup mock parser to raise error
        mock_parser = Mock()
        mock_parser.parse_from_text = AsyncMock(side_effect=Exception("Parsing failed"))
        mock_personal_education_parser_class.return_value = mock_parser

        from app.parser import cv_parser_workflow

        initial_state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        result = await cv_parser_workflow.ainvoke(initial_state)

        assert "errors" in result
        assert (
            "Personal info, education, and skills parsing failed" in result["errors"][0]
        )

    @pytest.mark.asyncio
    @patch("app.parser.workflow.nodes.PersonalInfoEducationAndSkillsParser")
    @patch("app.parser.workflow.nodes.WorkExperienceAndProjectsParser")
    @patch("app.parser.workflow.nodes.AdditionalSectionsAndCertificationsParser")
    async def test_workflow_execution_validation_error(
        self,
        mock_additional,
        mock_skills_work_projects,
        mock_personal_education,
    ):
        """Test workflow execution with validation error."""
        # Setup mock personal info parser to return data without personal info
        from app.parser.sections.personal_info import PersonalInfoEducationAndSkills

        personal_info = PersonalInfo()  # Empty personal info, no full_name
        combined_result = PersonalInfoEducationAndSkills(
            personal_info=personal_info, education=[], skills=[]
        )
        mock_personal_education_parser = Mock()
        mock_personal_education_parser.parse_from_text = AsyncMock(
            return_value=combined_result
        )
        mock_personal_education.return_value = mock_personal_education_parser

        # Setup combined skills, work experience, and projects parser
        mock_skills_work_projects_parser = Mock()
        mock_skills_work_projects_parser.parse_from_text = AsyncMock(
            return_value=([], [])
        )
        mock_skills_work_projects.return_value = mock_skills_work_projects_parser

        # Setup combined additional sections and certifications parser
        mock_additional_parser = Mock()
        mock_additional_parser.parse_from_text = AsyncMock(
            return_value={
                "languages": [],
                "volunteer_experience": [],
                "extracurricular_activities": [],
                "references": [],
                "certifications": [],
                "awards": [],
                "publications": [],
                "summary": "Experienced software engineer with 5+ years in Python development.",
            }
        )
        mock_additional.return_value = mock_additional_parser

        from app.parser import cv_parser_workflow

        initial_state: CVParserState = {
            "input_text": "Sample CV text",
            "input_file_base64": None,
            "filename": "test.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        result = await cv_parser_workflow.ainvoke(initial_state)

        assert "errors" in result
        assert "Could not extract basic personal information" in result["errors"][0]

    @pytest.mark.asyncio
    @patch("app.parser.workflow.nodes.ContentValidationParser")
    async def test_workflow_execution_early_termination_on_content_validation_failure(
        self, mock_validation_parser_class
    ):
        """Test workflow stops early when content validation fails with high confidence."""
        # Setup mock validation parser to fail with high confidence
        mock_validator = Mock()
        mock_validator.validate_content = AsyncMock(
            return_value={
                "is_cv_content": False,
                "confidence": 0.9,  # High confidence that it's NOT a CV
                "reasoning": "This appears to be a shopping list, not a CV",
            }
        )
        mock_validation_parser_class.return_value = mock_validator

        from app.parser import cv_parser_workflow

        initial_state: CVParserState = {
            "input_text": "Shopping list: milk, eggs, bread",  # Clearly not a CV
            "input_file_base64": None,
            "filename": "shopping_list.txt",
            "parsed_data": None,
            "errors": [],
            "mode": "text",
            "multimodal_full_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        result = await cv_parser_workflow.ainvoke(initial_state)

        # Should stop early with validation error
        assert "errors" in result
        assert "Content does not appear to be a CV/Resume" in result["errors"][0]
        assert "shopping list" in result["errors"][0]

        # Should NOT have any parsing results since it stopped early
        assert result.get("personal_info_result") is None
        assert result.get("education_result") is None
        assert result.get("parsed_data") is None


class TestParserEdgeCases:
    """Test edge cases and error scenarios."""

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parser_with_invalid_api_key(self, mock_chat_openai):
        """Test parser behavior with invalid API key."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Invalid API key")
        mock_llm.with_structured_output.return_value = mock_chain
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_text("Sample CV text", "test.pdf")

        assert "Error parsing CV text" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parser_with_timeout(self, mock_chat_openai):
        """Test parser behavior with timeout."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = TimeoutError("Request timeout")
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_text("Sample CV text", "test.pdf")

        assert "Error parsing CV text" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parser_with_rate_limit(self, mock_chat_openai):
        """Test parser behavior with rate limiting."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Rate limit exceeded")
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_text("Sample CV text", "test.pdf")

        assert "Error parsing CV text" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parser_with_very_long_text(self, mock_chat_openai):
        """Test parser with very long CV text."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        parsed_data = CVData(personal_info=PersonalInfo(full_name="John Doe"))
        mock_chain.ainvoke.return_value = parsed_data
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        # Very long text (100k characters)
        long_text = "A" * 100000

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            result = await parser.parse_from_text(long_text, "test.pdf")

        assert result.personal_info.full_name == "John Doe"
        mock_chain.ainvoke.assert_called_once_with(long_text)

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parser_with_special_characters(self, mock_chat_openai):
        """Test parser with special characters in text."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        parsed_data = CVData(
            personal_info=PersonalInfo(full_name="José María Martínez-González")
        )
        mock_chain.ainvoke.return_value = parsed_data
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        # Text with special characters
        special_text = "José María Martínez-González\n软件工程师\n🎓 教育背景"

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            result = await parser.parse_from_text(special_text, "test.pdf")

        assert result.personal_info.full_name == "José María Martínez-González"

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parser_with_empty_text(self, mock_chat_openai):
        """Test parser with empty text."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        # Parser might return empty data or error for empty text
        mock_chain.ainvoke.side_effect = Exception("No content to parse")
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_text_chain", return_value=mock_chain):
            with pytest.raises(Exception):
                await parser.parse_from_text("", "test.pdf")

    @pytest.mark.asyncio
    @patch("app.parser.cv_parser.ChatOpenAI")
    async def test_parser_multimodal_with_invalid_base64(self, mock_chat_openai):
        """Test multimodal parser with invalid base64 content."""
        mock_llm = Mock()
        mock_chain = AsyncMock()
        mock_chain.ainvoke.side_effect = Exception("Invalid base64 content")
        mock_chat_openai.return_value = mock_llm

        parser = CVParser()

        with patch.object(parser, "create_multimodal_chain", return_value=mock_chain):
            with pytest.raises(Exception) as exc_info:
                await parser.parse_from_file(
                    "invalid_base64", "application/pdf", "test.pdf"
                )

        assert "Error parsing CV with multimodal" in str(exc_info.value)

    @pytest.mark.asyncio
    @patch("app.parser.workflow.nodes.PersonalInfoEducationAndSkillsParser")
    @patch("app.parser.workflow.nodes.WorkExperienceAndProjectsParser")
    @patch("app.parser.workflow.nodes.AdditionalSectionsAndCertificationsParser")
    async def test_multimodal_parallel_api_call_optimization(
        self, mock_additional_parser, mock_work_parser, mock_personal_parser
    ):
        """Test that multimodal parsing uses parallel API calls for each section."""
        from app.parser import cv_parser_workflow

        # Mock the responses for each specialized parser
        mock_personal_result = PersonalInfoEducationAndSkills(
            personal_info=PersonalInfo(full_name="John Doe", email="<EMAIL>"),
            education=[Education(school="MIT", degree="BS Computer Science")],
            skills=[Skill(name="Python", category="Technical")],
        )

        mock_work_result = (
            [WorkExperience(company="TechCorp", job_title="Senior Engineer")],
            [Project(name="AI Assistant")],
        )

        mock_additional_result = {
            "languages": [Language(language="English", proficiency="Native")],
            "certifications": [Certification(name="AWS Certified")],
            "awards": [Award(title="Employee of the Year")],
            "publications": [],
            "references": [],
            "volunteer_experience": [],
            "extracurricular_activities": [],
            "summary": "Experienced software engineer",
        }

        # Set up mock parser instances
        mock_personal_instance = Mock()
        mock_personal_instance.parse_from_images = AsyncMock(
            return_value=mock_personal_result
        )
        mock_personal_parser.return_value = mock_personal_instance

        mock_work_instance = Mock()
        mock_work_instance.parse_from_images = AsyncMock(return_value=mock_work_result)
        mock_work_parser.return_value = mock_work_instance

        mock_additional_instance = Mock()
        mock_additional_instance.parse_from_images = AsyncMock(
            return_value=mock_additional_result
        )
        mock_additional_parser.return_value = mock_additional_instance

        # Set up initial state for multimodal parsing
        initial_state: CVParserState = {
            "input_text": None,
            "input_file_base64": "dGVzdA==",  # Valid base64 for "test"
            "filename": "test.pdf",
            "parsed_data": None,
            "errors": [],
            "mode": "multimodal",
            "multimodal_full_result": None,
            "prepared_images": None,
            "image_mime_type": None,
            "validation_result": None,
            "personal_info_result": None,
            "summary_result": None,
            "education_result": None,
            "work_experience_result": None,
            "skills_result": None,
            "projects_result": None,
            "certifications_result": None,
            "additional_sections_result": None,
        }

        # Mock PDF to image conversion
        with patch(
            "app.file_processors.PDFToImageConverter.convert_pdf_to_images"
        ) as mock_convert:
            mock_convert.return_value = ["mock_image_base64"]

            # Execute the workflow
            result = await cv_parser_workflow.ainvoke(initial_state)

        # Verify that each parser's parse_from_images was called
        mock_personal_instance.parse_from_images.assert_called_once()
        mock_work_instance.parse_from_images.assert_called_once()
        mock_additional_instance.parse_from_images.assert_called_once()

        # Verify that no errors occurred
        assert (
            "errors" not in result or not result["errors"]
        ), f"Errors occurred: {result.get('errors', [])}"

        # Verify that the result contains properly extracted data
        assert "parsed_data" in result
        assert (
            result["parsed_data"] is not None
        ), f"parsed_data is None, full result: {result}"
        assert result["parsed_data"].personal_info.full_name == "John Doe"
        assert result["parsed_data"].summary == "Experienced software engineer"
        assert len(result["parsed_data"].education) == 1
        assert len(result["parsed_data"].work_experience) == 1
        assert len(result["parsed_data"].skills) == 1
