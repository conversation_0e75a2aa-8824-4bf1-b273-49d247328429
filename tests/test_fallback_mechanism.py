import io
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import UploadFile

from app.config import settings
from app.file_processors import (
    TextExtractionResult,
    process_file_with_fallback,
    should_fallback_to_multimodal,
    validate_content_quality,
)


class TestContentValidation:
    """Test content quality validation functions"""

    def test_validate_content_quality_empty_content(self):
        """Test validation with empty content"""
        is_valid, reason = validate_content_quality("")
        assert not is_valid
        assert reason == "Empty content"

    def test_validate_content_quality_whitespace_only(self):
        """Test validation with whitespace-only content"""
        is_valid, reason = validate_content_quality("   \n\t  ")
        assert not is_valid
        assert reason == "Content contains only whitespace"

    def test_validate_content_quality_too_short(self):
        """Test validation with content below minimum threshold"""
        short_content = "Hi"
        is_valid, reason = validate_content_quality(short_content)
        assert not is_valid
        assert "Content too short" in reason
        assert str(len(short_content)) in reason
        assert str(settings.MIN_CONTENT_LENGTH_THRESHOLD) in reason

    def test_validate_content_quality_good_content(self):
        """Test validation with good quality content"""
        good_content = "<PERSON>\nSoftware Engineer\nEmail: <EMAIL>\nPhone: ******-123-4567\nExperience: 5 years in Python development\nSkills: Python, FastAPI, Django, JavaScript, React, Node.js, PostgreSQL, MongoDB\nEducation: Bachelor of Science in Computer Science, University of Technology, 2018\nProjects: E-commerce platform using Django and React, Real-time chat application with WebSocket"
        is_valid, reason = validate_content_quality(good_content)
        assert is_valid
        assert reason is None

    def test_validate_content_quality_garbled_content(self):
        """Test validation with high symbol ratio (garbled content)"""
        # Create content with many symbols
        garbled_content = "ấ@#$%^&*()_+{}|:<>?[]\\;'\",./" * 10  # High symbol ratio
        is_valid, reason = validate_content_quality(garbled_content)
        assert not is_valid
        assert "garbled" in reason.lower()

    def test_validate_content_quality_no_alphabetic_chars(self):
        """Test validation with content lacking alphabetic characters"""
        # Make content long enough to pass length check but with no alphabetic chars
        numeric_content = "123456789 098765432 111222333 444555666 777888999 000111222 987654321 123098765 432101122 233444555 666777888 999000111 222333444 555666777 888999000 1"
        is_valid, reason = validate_content_quality(numeric_content)
        assert not is_valid
        assert "meaningful text" in reason

    def test_validate_content_quality_few_words(self):
        """Test validation with content having too few words"""
        # Make content long enough but with only a few actual words (padded with spaces and periods)
        few_words_content = "Name John Email example " + "." * 150 + " " * 50
        is_valid, reason = validate_content_quality(few_words_content)
        assert not is_valid
        assert "too few words" in reason

    @patch.object(settings, "CONTENT_QUALITY_CHECK", False)
    def test_validate_content_quality_disabled_checks(self):
        """Test validation when quality checks are disabled"""
        # Even garbled content should pass if quality checks are disabled (but must meet length requirement)
        garbled_content = (
            "@#$%^&*()_+{}|:<>?[]\\;'\",./" * 10
        )  # Make it longer than 150 chars
        is_valid, reason = validate_content_quality(garbled_content)
        assert is_valid
        assert reason is None


class TestFallbackDecision:
    """Test fallback decision logic"""

    def test_should_fallback_extraction_failed(self):
        """Test fallback when text extraction completely failed"""
        failed_result = TextExtractionResult(
            success=False,
            text="",
            error_message="PDF extraction failed",
            extraction_method="pdftotext",
        )
        should_fallback, reason = should_fallback_to_multimodal(failed_result, True)
        assert should_fallback
        assert "Text extraction failed" in reason
        assert "PDF extraction failed" in reason

    def test_should_fallback_poor_quality_content(self):
        """Test fallback when content quality is poor"""
        poor_quality_result = TextExtractionResult(
            success=True,
            text="Hi",  # Too short
            error_message=None,
            extraction_method="pdftotext",
        )
        should_fallback, reason = should_fallback_to_multimodal(
            poor_quality_result, True
        )
        assert should_fallback
        assert "Poor content quality" in reason
        assert "too short" in reason.lower()

    def test_should_not_fallback_good_content(self):
        """Test no fallback when content is good quality"""
        good_result = TextExtractionResult(
            success=True,
            text="John Doe\nSoftware Engineer\nEmail: <EMAIL>\nPhone: ******-123-4567\nExperience: 5 years in Python development\nSkills: Python, FastAPI, Django, JavaScript, React, Node.js, PostgreSQL, MongoDB\nEducation: Bachelor of Science in Computer Science, University of Technology, 2018\nProjects: E-commerce platform using Django and React, Real-time chat application with WebSocket",
            error_message=None,
            extraction_method="pdftotext",
        )
        should_fallback, reason = should_fallback_to_multimodal(good_result, True)
        assert not should_fallback
        assert reason is None

    def test_should_not_fallback_disabled_multimodal(self):
        """Test no fallback when multimodal is disabled"""
        failed_result = TextExtractionResult(
            success=False,
            text="",
            error_message="PDF extraction failed",
            extraction_method="pdftotext",
        )
        should_fallback, reason = should_fallback_to_multimodal(failed_result, False)
        assert not should_fallback
        assert "Fallback disabled" in reason

    @patch.object(settings, "ENABLE_TEXTRACT_FALLBACK", False)
    def test_should_not_fallback_disabled_setting(self):
        """Test no fallback when fallback setting is disabled"""
        failed_result = TextExtractionResult(
            success=False,
            text="",
            error_message="PDF extraction failed",
            extraction_method="pdftotext",
        )
        should_fallback, reason = should_fallback_to_multimodal(failed_result, True)
        assert not should_fallback
        assert "Fallback disabled" in reason


class TestProcessFileWithFallback:
    """Test the main file processing with fallback function"""

    def create_mock_upload_file(self, content: bytes, filename: str) -> UploadFile:
        """Helper to create a mock UploadFile"""
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = filename
        mock_file.read = AsyncMock(return_value=content)
        mock_file.seek = AsyncMock()

        # Create a BytesIO object for file operations
        file_obj = io.BytesIO(content)
        mock_file.file = file_obj

        return mock_file

    @pytest.mark.asyncio
    async def test_forced_multimodal_mode(self):
        """Test forced multimodal mode"""
        content = b"dummy PDF content"
        mock_file = self.create_mock_upload_file(content, "test.pdf")

        with patch("app.file_processors.get_file_base64") as mock_get_base64:
            mock_get_base64.return_value = ("base64content", "application/pdf")

            result = await process_file_with_fallback(
                mock_file, {".pdf"}, 1000000, force_multimodal=True
            )

            assert result.mode_used == "multimodal"
            assert not result.fallback_triggered
            assert result.fallback_reason == "Forced multimodal mode"
            assert result.file_base64 == "base64content"

    @pytest.mark.asyncio
    async def test_successful_text_extraction(self):
        """Test successful text extraction without fallback"""
        content = b"dummy PDF content"
        mock_file = self.create_mock_upload_file(content, "test.pdf")

        good_text = "John Doe\nSoftware Engineer\nEmail: <EMAIL>\nPhone: ******-123-4567\nExperience: 5 years in Python development\nSkills: Python, FastAPI, Django, JavaScript, React, Node.js, PostgreSQL, MongoDB\nEducation: Bachelor of Science in Computer Science, University of Technology, 2018\nProjects: E-commerce platform using Django and React, Real-time chat application with WebSocket"

        with (
            patch("app.file_processors.FileProcessor.save_temp_file") as mock_save,
            patch(
                "app.file_processors.FileProcessorFactory.get_processor"
            ) as mock_factory,
        ):
            mock_save.return_value = "/tmp/test.pdf"

            # Mock processor that returns successful extraction
            mock_processor = MagicMock()
            mock_processor.try_extract_text_async = AsyncMock(
                return_value=TextExtractionResult(
                    success=True,
                    text=good_text,
                    error_message=None,
                    extraction_method="pdftotext",
                )
            )
            mock_factory.return_value = mock_processor

            with patch("os.path.exists", return_value=True), patch("os.unlink"):
                result = await process_file_with_fallback(mock_file, {".pdf"}, 1000000)

                assert result.mode_used == "text"
                assert not result.fallback_triggered
                assert result.text == good_text
                assert result.file_base64 is None

    @pytest.mark.asyncio
    async def test_fallback_to_multimodal_on_failure(self):
        """Test fallback to multimodal when text extraction fails"""
        content = b"dummy PDF content"
        mock_file = self.create_mock_upload_file(content, "test.pdf")

        with (
            patch("app.file_processors.FileProcessor.save_temp_file") as mock_save,
            patch(
                "app.file_processors.FileProcessorFactory.get_processor"
            ) as mock_factory,
            patch("app.file_processors.get_file_base64") as mock_get_base64,
        ):
            mock_save.return_value = "/tmp/test.pdf"
            mock_get_base64.return_value = ("base64content", "application/pdf")

            # Mock processor that returns failed extraction
            mock_processor = MagicMock()
            mock_processor.try_extract_text_async = AsyncMock(
                return_value=TextExtractionResult(
                    success=False,
                    text="",
                    error_message="PDF extraction failed",
                    extraction_method="pdftotext",
                )
            )
            mock_factory.return_value = mock_processor

            with patch("os.path.exists", return_value=True), patch("os.unlink"):
                result = await process_file_with_fallback(mock_file, {".pdf"}, 1000000)

                assert result.mode_used == "multimodal"
                assert result.fallback_triggered
                assert "Text extraction failed" in result.fallback_reason
                assert result.file_base64 == "base64content"

    @pytest.mark.asyncio
    async def test_fallback_to_multimodal_on_poor_content(self):
        """Test fallback to multimodal when content quality is poor"""
        content = b"dummy PDF content"
        mock_file = self.create_mock_upload_file(content, "test.pdf")

        with (
            patch("app.file_processors.FileProcessor.save_temp_file") as mock_save,
            patch(
                "app.file_processors.FileProcessorFactory.get_processor"
            ) as mock_factory,
            patch("app.file_processors.get_file_base64") as mock_get_base64,
        ):
            mock_save.return_value = "/tmp/test.pdf"
            mock_get_base64.return_value = ("base64content", "application/pdf")

            # Mock processor that returns successful extraction but poor content
            mock_processor = MagicMock()
            mock_processor.try_extract_text_async = AsyncMock(
                return_value=TextExtractionResult(
                    success=True,
                    text="Hi",  # Too short content
                    error_message=None,
                    extraction_method="pdftotext",
                )
            )
            mock_factory.return_value = mock_processor

            with patch("os.path.exists", return_value=True), patch("os.unlink"):
                result = await process_file_with_fallback(mock_file, {".pdf"}, 1000000)

                assert result.mode_used == "multimodal"
                assert result.fallback_triggered
                assert "Poor content quality" in result.fallback_reason
                assert result.file_base64 == "base64content"

    @pytest.mark.asyncio
    @patch.object(settings, "ENABLE_MULTIMODAL", False)
    async def test_fallback_disabled_multimodal_not_available(self):
        """Test error when fallback is needed but multimodal is disabled"""
        content = b"dummy PDF content"
        mock_file = self.create_mock_upload_file(content, "test.pdf")

        with (
            patch("app.file_processors.FileProcessor.save_temp_file") as mock_save,
            patch(
                "app.file_processors.FileProcessorFactory.get_processor"
            ) as mock_factory,
        ):
            mock_save.return_value = "/tmp/test.pdf"

            # Mock processor that returns failed extraction
            mock_processor = MagicMock()
            mock_processor.try_extract_text_async = AsyncMock(
                return_value=TextExtractionResult(
                    success=False,
                    text="",
                    error_message="PDF extraction failed",
                    extraction_method="pdftotext",
                )
            )
            mock_factory.return_value = mock_processor

            with (
                patch("os.path.exists", return_value=True),
                patch("os.unlink"),
                pytest.raises(Exception) as exc_info,
            ):
                await process_file_with_fallback(mock_file, {".pdf"}, 1000000)

                assert "multimodal fallback is disabled" in str(exc_info.value)


if __name__ == "__main__":
    pytest.main([__file__])
