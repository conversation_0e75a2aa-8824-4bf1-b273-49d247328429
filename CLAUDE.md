# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Management & Setup
- Use `uv` package manager for all Python operations
- Always run `uv run python` instead of `python` directly
- Use `uv sync` to install dependencies, `uv sync --dev` for development dependencies
- Main application startup: `uv run python main.py` (user handles manually)

### Code Quality & Linting
- Always run Ruff after code changes: `ruff check .` for linting, `ruff format .` for formatting
- Use `ruff check --fix .` to auto-fix issues when possible
- Pre-commit hooks are configured to run Ruff, pytest, and type checking

### Testing
- Write all test scripts in the `tests/` folder
- Always write tests with new features or requirements added
- Run tests: `uv run pytest tests/`
- Run specific test modules: `uv run pytest tests/test_api.py`
- Run tests with coverage: `uv run pytest tests/ --cov=app --cov-report=html`
- The project has 268 comprehensive tests covering all major functionality

### API Development
- Start development server: `uv run python main.py --verbose --reload`
- Production mode: `uv run python main.py --no-reload --quiet`
- API documentation available at `/docs` and `/redoc` endpoints
- Health check available at `/health`

## Architecture Overview

### Core Technologies
- **FastAPI** - High-performance web framework
- **LangChain** - LLM interactions with structured output using Pydantic
- **LangGraph** - Orchestrates parallel parsing workflow
- **OpenAI GPT-4o-mini** - Primary LLM for CV parsing with vision capabilities
- **Pydantic** - Data validation and serialization
- **Redis** - Optional caching layer

### Application Structure
```
app/
├── api.py              # FastAPI routes and endpoints
├── config.py           # Configuration management
├── models.py           # Pydantic data models for CV data
├── file_processors.py  # File handling (PDF, DOC, DOCX)
├── cached_parser.py    # Caching layer implementation
├── callback.py         # Async callback system
├── parser/             # Core parsing logic
│   ├── cv_parser.py    # Legacy single-parser implementation
│   ├── models.py       # Parser state models
│   ├── workflow/       # LangGraph workflow orchestration
│   │   ├── graph.py    # Workflow definition
│   │   └── nodes.py    # Workflow node implementations
│   ├── sections/       # Specialized section parsers
│   │   ├── personal_info.py    # Personal info, education, skills, summary
│   │   ├── work_experience.py  # Work experience and projects
│   │   └── additional.py       # Certifications, languages, etc.
│   └── validation/     # Content validation
│       └── content.py  # Text quality validation
└── utils.py           # Utility functions (phone formatting, etc.)
```

### Parallel Processing Workflow
The application uses LangGraph to orchestrate parallel parsing:

1. **Text Extraction** - Extract text from uploaded files
2. **Content Validation** - Validate extracted content quality
3. **Image Preparation** - Convert PDFs to images for multimodal parsing
4. **Parallel Section Parsing** - Three specialized parsers run concurrently:
   - PersonalInfoEducationAndSkillsParser
   - WorkExperienceAndProjectsParser
   - AdditionalSectionsAndCertificationsParser
5. **Result Aggregation** - Combine results from all parsers
6. **Final Validation** - Ensure data integrity

### Parsing Modes
- **Text Mode**: Extract text first, then parse with LLM (default)
- **Multimodal Mode**: Direct visual parsing using GPT-4o-mini vision
- **Intelligent Fallback**: Automatic switch to multimodal if text quality is poor

### Key Features
- **Parallel Processing**: 60-70% faster than sequential parsing
- **Intelligent Fallback**: Automatic multimodal fallback for poor text extraction
- **Phone Number Formatting**: Vietnamese phone numbers formatted to +84-xxxxxxxx
- **Request Tracking**: UUID4-based correlation across sync/async operations
- **Comprehensive Caching**: Redis-based with resilient fallback
- **File Support**: PDF, DOC, DOCX, and raw text input

## Data Models

### Core Data Structure
The CV data follows a hierarchical structure defined in `app/models.py`:
- **PersonalInfo**: Contact details, education level, gender, certifications
- **Education**: Schools, degrees, GPAs with structured types
- **WorkExperience**: Employment history with detailed descriptions
- **Skills**: Technical and soft skills categorization
- **Projects**: Personal/professional projects with technologies
- **Certifications**: Professional certifications with issuers
- **Additional Sections**: Languages, awards, publications, volunteer work

### Structured Types
- Gender: `"female" | "male" | "notToSay"`
- Education Level: `"highschool" | "diploma" | "bachelor" | "master" | "DoctorPhD"`
- GPA Type: `"4.0" | "10.0" | "5.0" | "letterGrade" | "classificationGrade"`
- English Certificates: `["ielts", "toeic", "toefl"]`

## Configuration

### Environment Variables
Key settings in `app/config.py`:
- **OpenAI**: `OPENAI_API_KEY`, `OPENAI_MODEL` (default: gpt-4o-mini)
- **Multimodal**: `PDF_TO_IMAGE_DPI` (200), `PDF_MAX_PAGES` (10)
- **Fallback**: `ENABLE_TEXTRACT_FALLBACK`, `MIN_CONTENT_LENGTH_THRESHOLD`
- **Caching**: `REDIS_URL`, `CACHE_TTL_SUCCESS`, `CACHE_ENABLED`
- **Logging**: `LOG_LEVEL` (DEBUG, INFO, WARNING, ERROR)

### File Processing
- **PDF**: Uses `pdftotext` for text extraction, `pdf2image` for multimodal
- **DOC**: Uses `antiword` for legacy Word documents
- **DOCX**: Uses `python-docx2txt` for modern Word documents
- **Max Size**: 10MB default, configurable via `MAX_FILE_SIZE`

## Testing Strategy

### Test Organization
- **API Tests**: Endpoint testing with FastAPI TestClient
- **Parser Tests**: Workflow and section parser unit tests
- **Integration Tests**: End-to-end parsing scenarios
- **File Processor Tests**: PDF conversion and text extraction
- **Fallback Tests**: Intelligent fallback mechanism validation
- **Performance Tests**: Benchmarking and load testing

### Test Fixtures
- Comprehensive fixtures in `tests/conftest.py` for mocking
- Sample CV data and file content for consistent testing
- Mock OpenAI responses and file processors
- Temporary file cleanup and error simulation

## Development Guidelines

### Code Style
- Follow existing patterns in the codebase
- Use type hints extensively (Pydantic models)
- Implement proper error handling with structured logging
- Use async/await for I/O operations
- Follow the modular parser architecture

### Adding New Features
1. Update Pydantic models in `app/models.py` if needed
2. Create or modify specialized parsers in `app/parser/sections/`
3. Update workflow in `app/parser/workflow/` if needed
4. Add comprehensive tests in `tests/`
5. Update API endpoints in `app/api.py`
6. Run linting and formatting before committing

### Performance Considerations
- Leverage parallel processing for new parsers
- Use caching for expensive operations
- Consider token costs for OpenAI API calls
- Monitor processing times and optimize bottlenecks

## API Endpoints

### Main Parsing Endpoints
- **POST /internal/v2/parse** - OpenAI-based parsing (text/multimodal)
- **POST /internal/v1/parse** - Legacy parser with callback support
- **GET /health** - Health check endpoint
- **GET /mock/callback** - Mock callback for testing

### Request/Response Format
- File upload with optional multimodal flag
- Raw text input with filename
- UUID4 request tracking for async operations
- Structured JSON response with metadata
