import os

from locust import Http<PERSON><PERSON>, between, task


class CVParserUser(HttpUser):
    wait_time = between(1, 3)

    def on_start(self):
        """Setup method called once per user when they start"""
        # Path to the PDF file
        self.pdf_path = "Hai.pdf"

        # Verify the file exists
        if not os.path.exists(self.pdf_path):
            raise FileNotFoundError(f"PDF file not found: {self.pdf_path}")

    @task(2)
    def test_async_file_upload_with_callback(self):
        """Test asynchronous file upload parsing with callback"""
        with open(self.pdf_path, "rb") as pdf_file:
            files = {"file": ("Hai.pdf", pdf_file, "application/pdf")}
            data = {
                "multimodal": "false",
                "callback_url": "http://localhost:8080/mock/callback",
            }

            with self.client.post(
                "/internal/v1/parse", files=files, data=data, catch_response=True
            ) as response:
                if response.status_code == 200:
                    json_response = response.json()
                    if json_response.get("status") == "processing":
                        response.success()
                    else:
                        response.failure(
                            f"Expected status=processing, got: {json_response}"
                        )
                else:
                    response.failure(f"HTTP {response.status_code}: {response.text}")
