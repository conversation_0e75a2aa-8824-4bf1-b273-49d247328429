import logging
from datetime import datetime

import httpx
from pydantic import BaseModel
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log,
)

from app.api import cached_cv_parser_workflow
from app.models import CVParseResponse
from app.parser import CVParserState

# FastAPI Router and Models
logger = logging.getLogger(__name__)


class CallbackPayload(BaseModel):
    """Payload sent to callback URL"""

    request_id: str
    status: str
    success: bool
    profile: dict
    meta_data: dict
    original_filename: str


def _should_retry_callback_error(exception: Exception) -> bool:
    """Determine if a callback error should trigger a retry"""
    if isinstance(exception, httpx.HTTPStatusError):
        # Retry on server errors (5xx) and specific client errors
        status_code = exception.response.status_code
        return status_code >= 500 or status_code in (408, 429)

    # Retry on connection and timeout errors
    return isinstance(
        exception,
        (
            httpx.ConnectError,
            httpx.TimeoutException,
            httpx.ReadTimeout,
            httpx.WriteTimeout,
            httpx.ConnectTimeout,
            httpx.PoolTimeout,
        ),
    )


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=1, max=60),
    retry=retry_if_exception_type(
        (
            httpx.HTTPStatusError,
            httpx.ConnectError,
            httpx.TimeoutException,
            httpx.ReadTimeout,
            httpx.WriteTimeout,
            httpx.ConnectTimeout,
            httpx.PoolTimeout,
        )
    ),
    before_sleep=before_sleep_log(logger, logging.WARNING),
    reraise=True,
)
async def send_callback(callback_url: str, payload: CallbackPayload) -> None:
    """Send callback notification to the provided URL with retry logic"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                callback_url,
                json=payload.model_dump(),
                headers={"Content-Type": "application/json"},
            )
            response.raise_for_status()
            logger.info(f"Callback sent successfully to {callback_url}")
    except httpx.HTTPStatusError as e:
        # Check if this is a retryable error
        if not _should_retry_callback_error(e):
            logger.error(
                f"Non-retryable HTTP error for callback to {callback_url}: {e.response.status_code} {e.response.reason_phrase}"
            )
            return
        # Re-raise for retry logic to handle
        raise
    except Exception as e:
        logger.error(f"Failed to send callback to {callback_url}: {str(e)}")
        raise


async def process_cv_with_callback(
    initial_state: CVParserState,
    callback_url: str,
    request_id: str,
    original_filename: str,
) -> None:
    """Process CV with parser and send callback with response format"""
    try:
        # Run the CV parsing workflow with caching
        result = await cached_cv_parser_workflow.ainvoke(initial_state)

        # Check for errors
        if result.get("errors") and result["errors"]:
            error_msg = "; ".join(result["errors"])
            logger.error(f"[{request_id}] Parsing errors: {error_msg}")

            # Send error callback
            error_payload = CallbackPayload(
                request_id=request_id,
                status="failed",
                success=False,
                profile={},
                meta_data={
                    "cv_text": "",
                    "parser_time": datetime.now().isoformat(),
                    "error": error_msg,
                },
                original_filename=original_filename,
            )
            await send_callback(callback_url, error_payload)
            return

        if result.get("error"):
            logger.error(f"[{request_id}] Parsing error: {result['error']}")

            # Send error callback
            error_payload = CallbackPayload(
                request_id=request_id,
                status="failed",
                success=False,
                profile={},
                meta_data={
                    "cv_text": "",
                    "parser_time": datetime.now().isoformat(),
                    "error": result["error"],
                },
                original_filename=original_filename,
            )
            await send_callback(callback_url, error_payload)
            return

        # Get parsed data
        parsed_data = result.get("parsed_data")
        if not parsed_data:
            error_msg = "Failed to parse CV data"
            logger.error(f"[{request_id}] {error_msg}")

            # Send error callback
            error_payload = CallbackPayload(
                request_id=request_id,
                status="failed",
                success=False,
                profile={},
                meta_data={
                    "cv_text": "",
                    "parser_time": datetime.now().isoformat(),
                    "error": error_msg,
                },
                original_filename=original_filename,
            )
            await send_callback(callback_url, error_payload)
            return

        response = CVParseResponse(
            request_id=request_id,
            status="succeeded",
            success=True,
            profile=parsed_data.model_dump(),
            meta_data={
                "cv_text": "",
                "parser_time": datetime.now().isoformat(),
            },
        )

        payload = CallbackPayload(
            request_id=request_id,
            status="succeeded",
            success=True,
            profile=response.profile,
            meta_data=response.meta_data,
            original_filename=original_filename,
        )

        await send_callback(callback_url, payload)

        logger.info(
            f"[{request_id}] Successfully processed CV with callback for: {parsed_data.personal_info.full_name if parsed_data.personal_info else 'Unknown'}"
        )

    except Exception as e:
        logger.error(f"[{request_id}] Error processing CV with callback: {str(e)}")

        # Send error callback
        error_payload = CallbackPayload(
            request_id=request_id,
            status="failed",
            success=False,
            profile={},
            meta_data={
                "cv_text": "",
                "parser_time": datetime.now().isoformat(),
                "error": str(e),
            },
            original_filename=original_filename,
        )
        await send_callback(callback_url, error_payload)
