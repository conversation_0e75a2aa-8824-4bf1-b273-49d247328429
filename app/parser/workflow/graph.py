import logging
from langgraph.graph import <PERSON><PERSON>, StateGraph
from langgraph.graph.state import CompiledStateGraph

from app.parser.models import CVParserState
from .nodes import (
    aggregate_results_node,
    extract_text_node,
    parse_all_sections_parallel_node,
    prepare_images_node,
    validate_content_node,
    validation_node,
)

logger = logging.getLogger(__name__)


def should_continue_after_validation(state: CVParserState) -> str:
    """Conditional routing function to decide whether to continue processing or stop early"""
    # Check for validation errors or other errors that should stop processing
    if state.get("errors"):
        logger.info(f"Stopping workflow early due to errors: {state['errors']}")
        return END

    # Check if content validation failed
    validation_result = state.get("validation_result")
    if validation_result and not validation_result.get("is_cv_content", True):
        logger.info(
            f"Stopping workflow early - content validation failed: {validation_result.get('reasoning', 'Unknown reason')}"
        )
        return END

    # Continue to prepare images if validation passed
    return "prepare_images"


def create_cv_parser_workflow() -> CompiledStateGraph:
    """Create the CV parsing workflow using LangGraph with parallel section parsing"""
    workflow = StateGraph(CVParserState)

    # Add nodes
    workflow.add_node("extract_text", extract_text_node)
    workflow.add_node("validate_content", validate_content_node)
    workflow.add_node("prepare_images", prepare_images_node)

    # Combined parallel parsing node
    workflow.add_node("parse_all_sections_parallel", parse_all_sections_parallel_node)

    # Aggregation and validation nodes
    workflow.add_node("aggregate_results", aggregate_results_node)
    workflow.add_node("validate", validation_node)

    # Route through content validation first, with conditional routing
    # validate_content_node validates content is CV-like, then decide whether to continue or stop early
    workflow.add_edge("extract_text", "validate_content")
    workflow.add_conditional_edges(
        "validate_content",
        should_continue_after_validation,
        {
            "prepare_images": "prepare_images",
            END: END,
        },
    )

    # After image preparation (or skip), go to combined parallel parser
    workflow.add_edge("prepare_images", "parse_all_sections_parallel")

    # Combined parallel node feeds into aggregation
    workflow.add_edge("parse_all_sections_parallel", "aggregate_results")

    # Final validation and end
    workflow.add_edge("aggregate_results", "validate")
    workflow.add_edge("validate", END)

    # Set entry point
    workflow.set_entry_point("extract_text")

    return workflow.compile()


# Create singleton instance of the workflow
cv_parser_workflow: CompiledStateGraph = create_cv_parser_workflow()
