import asyncio
import base64
import logging
import time
from typing import Any

from app.config import settings
from app.models import CVData
from app.parser.models import CVParserState
from app.parser.sections import (
    AdditionalSectionsAndCertificationsParser,
    PersonalInfoEducationAndSkillsParser,
    WorkExperienceAndProjectsParser,
)
from app.parser.validation import ContentValidationParser
from app.utils import normalize_phone_number_async

logger = logging.getLogger(__name__)


async def extract_text_node(state: CVParserState) -> dict[str, Any]:
    """Node for text extraction (already done in API layer)"""
    # Text extraction is handled in the API layer
    # This node just validates the state and logs fallback information
    filename = state.get("filename", "unknown")
    mode = state.get("mode", "unknown")
    fallback_triggered = state.get("fallback_triggered", False)
    fallback_reason = state.get("fallback_reason")

    # Log workflow start with fallback information
    if fallback_triggered:
        logger.info(
            f"Starting CV parsing workflow for {filename} in {mode} mode (fallback triggered: {fallback_reason})"
        )
    else:
        logger.info(f"Starting CV parsing workflow for {filename} in {mode} mode")

    if state["mode"] == "text" and not state.get("input_text"):
        return {"errors": ["No text provided for text mode parsing"]}
    elif state["mode"] == "multimodal" and not state.get("input_file_base64"):
        return {"errors": ["No file data provided for multimodal parsing"]}
    return {}


async def validate_content_node(state: CVParserState) -> dict[str, Any]:
    """Node for validating if input content appears to be CV/Resume content"""
    if state.get("errors"):
        return {}

    try:
        # Skip validation for multimodal mode - assume files uploaded are CVs
        if state["mode"] == "multimodal":
            logger.debug("Skipping content validation for multimodal mode")
            return {
                "validation_result": {
                    "is_cv_content": True,
                    "confidence": 1.0,
                    "reasoning": "File upload assumed to be CV/Resume content",
                }
            }

        # Validate text content
        input_text = state.get("input_text")
        if not input_text:
            return {"errors": ["No text content available for validation"]}

        logger.info(f"Validating content for file: {state['filename']}")
        validator = ContentValidationParser()
        validation_result = await validator.validate_content(input_text)

        # Check if content appears to be CV/Resume content
        if not validation_result["is_cv_content"]:
            # If confidence is low, give it a chance anyway
            if validation_result["confidence"] < 0.9:
                logger.warning(
                    f"Content validation uncertain for {state['filename']}: {validation_result['reasoning']} "
                    f"(confidence: {validation_result['confidence']:.2f}) - proceeding anyway"
                )
                validation_result["is_cv_content"] = False
            else:
                logger.error(
                    f"Content does not appear to be CV/Resume for {state['filename']}: {validation_result['reasoning']} "
                    f"(confidence: {validation_result['confidence']:.2f})"
                )
                return {
                    "errors": [
                        f"Content does not appear to be a CV/Resume: {validation_result['reasoning']} "
                        f"(confidence: {validation_result['confidence']:.2f})"
                    ]
                }

        logger.info(
            f"Content validation passed for {state['filename']}: {validation_result['reasoning']} "
            f"(confidence: {validation_result['confidence']:.2f})"
        )

        return {"validation_result": validation_result}

    except Exception as e:
        logger.error(
            f"Content validation failed for {state['filename']}: {str(e)}",
            exc_info=True,
        )
        # On validation error, be permissive and allow processing
        return {
            "validation_result": {
                "is_cv_content": True,
                "confidence": 0.5,
                "reasoning": f"Validation error occurred, allowing processing: {str(e)}",
            }
        }


async def prepare_images_node(state: CVParserState) -> dict[str, Any]:
    """Node for preparing images from PDF for multimodal parsing"""
    if state.get("errors"):
        return {}

    if state["mode"] != "multimodal":
        logger.info("Skipping image preparation - mode is text")
        return {}  # Skip this node for text mode

    try:
        logger.info(f"Preparing images for multimodal parsing: {state['filename']}")
        mime_type = "application/pdf"

        # Handle PDF files - convert to images
        if mime_type == "application/pdf":
            from app.file_processors import PDFToImageConverter

            logger.info(
                f"Converting PDF to images for multimodal processing: {state['filename']}"
            )

            # Decode base64 to bytes (CPU-intensive operation in thread pool)
            pdf_bytes = await asyncio.to_thread(
                base64.b64decode, state["input_file_base64"]
            )

            # Convert PDF to images (now async)
            image_base64_list = await PDFToImageConverter.convert_pdf_to_images(
                pdf_bytes, state["filename"]
            )

            # Store image info in state for parallel parsers
            image_format = settings.PDF_IMAGE_FORMAT.lower()
            mime_type_image = f"image/{image_format}"

            logger.info(
                f"Successfully converted PDF to {len(image_base64_list)} images for {state['filename']}"
            )

            # Add images to state for parallel parsing
            # Adding a new state field to avoid breaking existing code
            return {
                "prepared_images": image_base64_list,
                "image_mime_type": mime_type_image,
            }
        else:
            # Non-PDF files - use directly (for future support)
            return {
                "prepared_images": [state["input_file_base64"]],
                "image_mime_type": mime_type,
            }

    except Exception as e:
        logger.error(
            f"Image preparation failed for file {state['filename']}: {str(e)}",
            exc_info=True,
        )
        return {"errors": [f"Image preparation failed: {str(e)}"]}


async def parse_personal_info_and_education_node(
    state: CVParserState,
) -> dict[str, Any]:
    """Node for parsing personal information, education, and skills sections"""
    if state.get("errors"):
        return {}

    try:
        if state["mode"] == "text":
            parser = PersonalInfoEducationAndSkillsParser()
            combined_result = await parser.parse_from_text(state["input_text"])
            return {
                "personal_info_result": combined_result.personal_info,
                "education_result": combined_result.education,
                "skills_result": combined_result.skills,
            }
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                # Normalize phone number for multimodal results too
                personal_info = multimodal_result.personal_info
                if personal_info and personal_info.phone:
                    personal_info.phone = await normalize_phone_number_async(
                        personal_info.phone
                    )
                    logger.debug(
                        f"Normalized multimodal phone number to: {personal_info.phone}"
                    )

                return {
                    "personal_info_result": personal_info,
                    "education_result": multimodal_result.education,
                    "skills_result": multimodal_result.skills,
                }
            else:
                return {
                    "errors": [
                        "No multimodal result available for personal info and education parsing"
                    ]
                }
    except Exception as e:
        return {
            "errors": [f"Personal info, education, and skills parsing failed: {str(e)}"]
        }


async def parse_work_experience_and_projects_node(
    state: CVParserState,
) -> dict[str, Any]:
    """Combined node for parsing work experience and projects sections"""
    if state.get("errors"):
        return {}

    try:
        parser = WorkExperienceAndProjectsParser()
        if state["mode"] == "text":
            work_experience, projects = await parser.parse_from_text(
                state["input_text"]
            )
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                work_experience = multimodal_result.work_experience
                projects = multimodal_result.projects
            else:
                return {
                    "errors": [
                        "No multimodal result available for work experience and projects parsing"
                    ]
                }

        return {
            "work_experience_result": work_experience,
            "projects_result": projects,
        }
    except Exception as e:
        return {"errors": [f"Work experience and projects parsing failed: {str(e)}"]}


async def parse_additional_sections_and_certifications_node(
    state: CVParserState,
) -> dict[str, Any]:
    """Node for parsing additional sections and certifications (languages, volunteer work, certifications, awards, publications, etc.)"""
    if state.get("errors"):
        return {}

    try:
        parser = AdditionalSectionsAndCertificationsParser()
        if state["mode"] == "text":
            result = await parser.parse_from_text(state["input_text"])
        else:  # multimodal - extract from stored full result
            multimodal_result = state.get("multimodal_full_result")
            if multimodal_result:
                result = {
                    "languages": multimodal_result.languages,
                    "volunteer_experience": multimodal_result.volunteer_experience,
                    "extracurricular_activities": multimodal_result.extracurricular_activities,
                    "references": multimodal_result.references,
                    "certifications": multimodal_result.certifications,
                    "awards": multimodal_result.awards,
                    "publications": multimodal_result.publications,
                    "summary": multimodal_result.summary,
                }
            else:
                return {
                    "errors": [
                        "No multimodal result available for additional sections and certifications parsing"
                    ]
                }

        return {"additional_sections_result": result}
    except Exception as e:
        return {
            "errors": [
                f"Additional sections and certifications parsing failed: {str(e)}"
            ]
        }


async def parse_all_sections_parallel_node(state: CVParserState) -> dict[str, Any]:
    """Combined node that runs all section parsers in parallel for better performance"""
    if state.get("errors"):
        return {}

    try:
        if state["mode"] == "text":
            # Create parser instances
            personal_parser = PersonalInfoEducationAndSkillsParser()
            work_parser = WorkExperienceAndProjectsParser()
            additional_parser = AdditionalSectionsAndCertificationsParser()

            # Run all parsers in parallel using asyncio.gather
            logger.info("Running all section parsers in parallel for text mode")
            start_time = time.time()

            tasks = [
                personal_parser.parse_from_text(state["input_text"]),
                work_parser.parse_from_text(state["input_text"]),
                additional_parser.parse_from_text(state["input_text"]),
            ]

            # Execute all parsers concurrently with exception handling
            results = await asyncio.gather(*tasks, return_exceptions=True)

            end_time = time.time()
            logger.info(
                f"Parallel parsing completed in {end_time - start_time:.2f} seconds"
            )

            # Process results and handle any exceptions
            errors = []
            result_dict = {}

            # Handle personal info/education/skills result
            if isinstance(results[0], Exception):
                error_msg = f"Personal info, education, and skills parsing failed: {str(results[0])}"
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                personal_result = results[0]
                result_dict.update(
                    {
                        "personal_info_result": personal_result.personal_info,
                        "education_result": personal_result.education,
                        "skills_result": personal_result.skills,
                    }
                )
                logger.info(
                    f"Personal info parser: {len(personal_result.education)} education, {len(personal_result.skills)} skills"
                )

            # Handle work experience and projects result
            if isinstance(results[1], Exception):
                error_msg = (
                    f"Work experience and projects parsing failed: {str(results[1])}"
                )
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                work_experience, projects = results[1]
                result_dict.update(
                    {
                        "work_experience_result": work_experience,
                        "projects_result": projects,
                    }
                )
                logger.info(
                    f"Work parser: {len(work_experience)} work experiences, {len(projects)} projects"
                )

            # Handle additional sections result
            if isinstance(results[2], Exception):
                error_msg = f"Additional sections and certifications parsing failed: {str(results[2])}"
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                additional_result = results[2]
                result_dict["additional_sections_result"] = additional_result
                logger.info(
                    f"Additional parser: {len(additional_result.get('languages', []))} languages, {len(additional_result.get('certifications', []))} certifications"
                )

            # Add any errors to the result
            if errors:
                result_dict["errors"] = errors

            return result_dict

        else:  # multimodal mode
            # Check if images are prepared
            prepared_images = state.get("prepared_images")
            image_mime_type = state.get("image_mime_type")

            if not prepared_images:
                return {
                    "errors": ["No prepared images available for multimodal parsing"]
                }

            # Create parser instances
            personal_parser = PersonalInfoEducationAndSkillsParser()
            work_parser = WorkExperienceAndProjectsParser()
            additional_parser = AdditionalSectionsAndCertificationsParser()

            # Run all parsers in parallel using asyncio.gather
            logger.info("Running all section parsers in parallel for multimodal mode")
            start_time = time.time()

            tasks = [
                personal_parser.parse_from_images(
                    prepared_images, image_mime_type, state["filename"]
                ),
                work_parser.parse_from_images(
                    prepared_images, image_mime_type, state["filename"]
                ),
                additional_parser.parse_from_images(
                    prepared_images, image_mime_type, state["filename"]
                ),
            ]

            # Execute all parsers concurrently with exception handling
            results = await asyncio.gather(*tasks, return_exceptions=True)

            end_time = time.time()
            logger.info(
                f"Parallel multimodal parsing completed in {end_time - start_time:.2f} seconds"
            )

            # Log summary note about token usage tracking
            logger.info(
                "Multimodal Token Usage Summary: Individual parser token usage logged above. "
                "Check previous log entries for detailed cost breakdown per section."
            )

            # Process results and handle any exceptions
            errors = []
            result_dict = {}

            # Handle personal info/education/skills result
            if isinstance(results[0], Exception):
                error_msg = f"Multimodal personal info, education, and skills parsing failed: {str(results[0])}"
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                personal_result = results[0]
                result_dict.update(
                    {
                        "personal_info_result": personal_result.personal_info,
                        "education_result": personal_result.education,
                        "skills_result": personal_result.skills,
                    }
                )
                logger.info(
                    f"Multimodal personal info parser: {len(personal_result.education)} education, {len(personal_result.skills)} skills"
                )

            # Handle work experience and projects result
            if isinstance(results[1], Exception):
                error_msg = f"Multimodal work experience and projects parsing failed: {str(results[1])}"
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                work_experience, projects = results[1]
                result_dict.update(
                    {
                        "work_experience_result": work_experience,
                        "projects_result": projects,
                    }
                )
                logger.info(
                    f"Multimodal work parser: {len(work_experience)} work experiences, {len(projects)} projects"
                )

            # Handle additional sections result
            if isinstance(results[2], Exception):
                error_msg = f"Multimodal additional sections and certifications parsing failed: {str(results[2])}"
                logger.error(error_msg)
                errors.append(error_msg)
            else:
                additional_result = results[2]
                result_dict["additional_sections_result"] = additional_result
                logger.info(
                    f"Multimodal additional parser: {len(additional_result.get('languages', []))} languages, {len(additional_result.get('certifications', []))} certifications"
                )

            # Add any errors to the result
            if errors:
                result_dict["errors"] = errors

            return result_dict

    except Exception as e:
        logger.error(f"Combined parallel parsing failed: {str(e)}", exc_info=True)
        return {"errors": [f"Combined parallel parsing failed: {str(e)}"]}


async def aggregate_results_node(state: CVParserState) -> dict[str, Any]:
    """Node for aggregating all parallel parsing results into final CVData"""
    if state.get("errors"):
        return {
            "errors": [
                f"Cannot aggregate due to previous errors: {'; '.join(state['errors'])}"
            ]
        }

    try:
        # Extract all parsed sections
        personal_info = state.get("personal_info_result")
        education = state.get("education_result") or []
        work_experience = state.get("work_experience_result") or []
        skills = state.get("skills_result") or []
        projects = state.get("projects_result") or []

        # Extract additional sections results (includes languages, volunteer work, activities, references, certifications, awards, publications, summary)
        additional_results = state.get("additional_sections_result") or {}
        languages = additional_results.get("languages", [])
        volunteer_experience = additional_results.get("volunteer_experience", [])
        extracurricular_activities = additional_results.get(
            "extracurricular_activities", []
        )
        references = additional_results.get("references", [])
        certifications = additional_results.get("certifications", [])
        awards = additional_results.get("awards", [])
        publications = additional_results.get("publications", [])
        summary = additional_results.get(
            "summary"
        )  # Summary now extracted by AdditionalSectionsParser

        # Aggregate into final CVData object
        parsed_data = CVData(
            personal_info=personal_info,
            summary=summary,  # Summary now extracted by AdditionalSectionsParser
            education=education,
            work_experience=work_experience,
            skills=skills,
            projects=projects,
            certifications=certifications,
            awards=awards,
            languages=languages,
            publications=publications,
            references=references,
            volunteer_experience=volunteer_experience,
            extracurricular_activities=extracurricular_activities,
        )

        # Log successful aggregation with fallback information
        filename = state.get("filename", "unknown")
        mode = state.get("mode", "unknown")
        fallback_triggered = state.get("fallback_triggered", False)
        fallback_reason = state.get("fallback_reason")

        if fallback_triggered:
            logger.info(
                f"Successfully aggregated CV data for {filename} using {mode} mode (fallback: {fallback_reason})"
            )
        else:
            logger.info(
                f"Successfully aggregated CV data for {filename} using {mode} mode"
            )

        return {"parsed_data": parsed_data}
    except Exception as e:
        return {"errors": [f"Results aggregation failed: {str(e)}"]}


def validation_node(state: CVParserState) -> dict[str, Any]:
    """Node for validating parsed data"""
    if state.get("errors"):
        return {}

    parsed_data = state.get("parsed_data")
    if not parsed_data:
        return {"errors": ["No parsed data available"]}

    # Basic validation - ensure at least some key information was extracted
    if not parsed_data.personal_info or not parsed_data.personal_info.full_name:
        return {"errors": ["Could not extract basic personal information from CV"]}

    return {}
