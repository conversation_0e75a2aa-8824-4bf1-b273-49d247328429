import operator
from typing import Annotated, Any, TypedDict

from app.models import (
    Certification,
    CVData,
    Education,
    PersonalInfo,
    Project,
    Skill,
    WorkExperience,
)


class CVParserState(TypedDict):
    """State for CV parsing workflow"""

    input_text: str | None
    input_file_base64: str | None
    filename: str
    parsed_data: CVData | None
    errors: Annotated[list[str], operator.add]  # Collect errors from parallel nodes
    mode: str  # "text" or "multimodal"
    multimodal_full_result: (
        CVData | None
    )  # Stores complete result from single multimodal API call
    validation_result: dict[str, Any] | None  # Content validation results

    # Fallback tracking
    fallback_triggered: bool | None  # Whether fallback to multimodal was triggered
    fallback_reason: str | None  # Reason for fallback (if triggered)
    original_extraction_method: (
        str | None
    )  # Original text extraction method used (if any)

    # Prepared images for multimodal parsing
    prepared_images: list[str] | None  # Base64 encoded images for multimodal parsing
    image_mime_type: str | None  # MIME type for the images

    # Parallel parsing results
    personal_info_result: PersonalInfo | None
    summary_result: str | None  # Summary extracted by PersonalInfoParser
    education_result: (
        list[Education] | None
    )  # Will be populated from personal_info_result
    skills_result: list[Skill] | None  # Will be populated from personal_info_result
    work_experience_result: list[WorkExperience] | None
    projects_result: list[Project] | None
    certifications_result: list[Certification] | None
    additional_sections_result: dict[str, Any] | None
