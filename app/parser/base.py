import logging
import time
from abc import ABC, abstractmethod
from typing import Any

from langchain.prompts import ChatPromptTemplate
from langchain.schema import HumanMessage
from langchain.schema.runnable import RunnablePassthrough
from langchain_community.callbacks import get_openai_callback
from langchain_openai import ChatOpenAI
from pydantic import BaseModel

from app.config import settings

logger = logging.getLogger(__name__)


class BaseParser(ABC):
    """Base parser class with common functionality for all CV section parsers"""

    def __init__(self, temperature: float = None):
        """Initialize parser with OpenAI client"""
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=temperature or settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

    @abstractmethod
    def get_text_prompt_messages(self) -> list[tuple[str, str]]:
        """Return the prompt messages for text-based parsing"""
        pass

    @abstractmethod
    def get_multimodal_prompt_text(self) -> str:
        """Return the prompt text for multimodal parsing"""
        pass

    @abstractmethod
    def get_output_model(self) -> type[BaseModel]:
        """Return the Pydantic model for structured output"""
        pass

    def create_text_chain(self):
        """Create chain for text-based parsing"""
        prompt = ChatPromptTemplate.from_messages(self.get_text_prompt_messages())
        return (
            {"cv_text": RunnablePassthrough()}
            | prompt
            | self.llm.with_structured_output(self.get_output_model())
        )

    def create_multimodal_chain(self):
        """Create chain for multimodal parsing"""
        return self.llm.with_structured_output(self.get_output_model())

    async def parse_from_text_with_logging(self, text: str, parser_name: str) -> Any:
        """Parse from text with standardized logging and error handling"""
        try:
            logger.info(f"{parser_name}: Starting text extraction")
            start_time = time.time()

            chain = self.create_text_chain()
            result = await chain.ainvoke(text)

            end_time = time.time()
            logger.info(
                f"{parser_name}: Extraction took {end_time - start_time:.2f} seconds"
            )
            return result
        except Exception as e:
            logger.error(
                f"{parser_name}: Error extracting from text: {str(e)}",
                exc_info=True,
            )
            raise

    async def parse_from_images_with_logging(
        self,
        image_base64_list: list[str],
        mime_type: str,
        filename: str,
        parser_name: str,
    ) -> Any:
        """Parse from images with standardized logging and error handling"""
        try:
            logger.info(
                f"{parser_name}: Starting multimodal extraction for file: {filename}"
            )
            start_time = time.time()

            # Create message content with text prompt and all images
            content = [{"type": "text", "text": self.get_multimodal_prompt_text()}]

            # Add each image to the content
            for i, img_base64 in enumerate(image_base64_list):
                content.append(
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:{mime_type};base64,{img_base64}"},
                    }
                )
                logger.debug(f"Added page {i + 1} to multimodal request")

            # Create message with content
            messages = [HumanMessage(content=content)]

            logger.debug(f"Sending request to OpenAI Vision API for {parser_name}")
            chain = self.create_multimodal_chain()

            # Track token usage for multimodal API call
            with get_openai_callback() as cb:
                result = await chain.ainvoke(messages)

            end_time = time.time()

            # Log comprehensive token usage information
            logger.info(
                f"{parser_name}: Token usage - Input: {cb.prompt_tokens:,}, "
                f"Output: {cb.completion_tokens:,}, Total: {cb.total_tokens:,}, "
                f"Cost: ${cb.total_cost:.4f}, Time: {end_time - start_time:.1f}s"
            )

            return result
        except Exception as e:
            logger.error(
                f"{parser_name}: Error in multimodal extraction: {str(e)}",
                exc_info=True,
            )
            raise

    def get_parser_name(self) -> str:
        """Get the parser name for logging purposes"""
        return self.__class__.__name__
