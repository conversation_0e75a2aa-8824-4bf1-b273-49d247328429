import logging

from pydantic import BaseModel

from app.models import Education, PersonalInfo, Skill
from app.parser.base import BaseParser
from app.utils import normalize_phone_number_async

logger = logging.getLogger(__name__)


class PersonalInfoEducationAndSkills(BaseModel):
    """Combined model for personal info, education, and skills extraction"""

    personal_info: PersonalInfo
    education: list[Education]
    skills: list[Skill]


class PersonalInfoEducationAndSkillsParser(BaseParser):
    """Parser specialized for personal information, education, and skills sections"""

    def get_text_prompt_messages(self) -> list[tuple[str, str]]:
        """Return the prompt messages for text-based parsing"""
        return [
            (
                "system",
                """You are an expert at extracting personal information, education, and skills from CV/Resume text.

Extract personal contact information, education background, and skills:

Personal Information:
- full_name: Complete name of the candidate
- email: Email address for contact
- phone: Phone number including country code if available (format as +84-xxxxxxxxx for Vietnamese numbers)
- location: Current city, state/province, and country
- graduation_year: Year of graduation from highest degree
- education_level: Highest level of education completed (must be one of: "highschool", "diploma", "associateDegree", "bachelor", "engineering", "master", "DoctorPhD")
- english_certificate: English proficiency certificates held (can include: "ielts", "toeic", "toefl")
- date_of_birth: Date of birth in any format mentioned
- gender: Gender identity if specified (must be one of: "female", "male", "notToSay")
- nationality: Country of citizenship
- linkedin_url: LinkedIn profile URL
- github_url: GitHub profile URL
- personal_website: Personal portfolio or website URL

Education Information:
- school: Name of the educational institution
- degree: Type of degree or diploma obtained
- field_of_study: Major, specialization, or field of study
- start_date: Start date of education in any format
- end_date: End date or expected graduation date
- gpa: Grade point average or academic score (do not include scale in gpa, only show gpa value). If GPA is on a 100-point scale, convert it to 10-point scale by dividing by 10.
- gpa_type: GPA scale system (must be one of: "4.0", "10.0", "5.0", "letterGrade", "classificationGrade", "germany"). Use "10.0" for converted 100-point scales.
- description: Additional details, coursework, or achievements

Skills Information:
- name: Name of the skill or technology
- category: Type of skill (must be either "Technical" for programming/tools or "Soft" for communication/leadership)

Guidelines:
- Extract only what is explicitly mentioned
- Don't invent or assume information
- Return null for missing fields
- Be precise with contact details
- For phone numbers: If the number appears to be Vietnamese (starts with 0, +84, or 84), format it as +84-xxxxxxxxx (remove leading 0 if present, add +84- prefix)
- For graduation_year: Only extract if explicitly mentioned and must be one of the allowed values
- For education_level: Infer from highest degree mentioned (PhD/Doctorate = "DoctorPhD", Master's = "master", Bachelor's/Engineering = "bachelor"/"engineering", Associate = "associateDegree", Diploma = "diploma", High School = "highschool")
- For english_certificate: Only include if IELTS, TOEIC, or TOEFL certificates are explicitly mentioned
- For education: List all educational institutions in reverse chronological order (most recent first)
- Parse education dates consistently (YYYY-MM format preferred)
- Include all educational institutions (universities, colleges, schools)
- For GPA: If a 100-point scale GPA is mentioned, convert it to 10-point scale by dividing by 10 and set gpa_type to "10.0"
- For skills: categorize each skill as either "Technical" or "Soft" - no other categories allowed
- Extract both explicitly listed skills and skills mentioned in descriptions
- Don't duplicate skills - each unique skill should appear only once""",
            ),
            (
                "human",
                "Extract personal information, education, and skills from this CV text:\n\n{cv_text}",
            ),
        ]

    def get_multimodal_prompt_text(self) -> str:
        """Return the prompt text for multimodal parsing"""
        return """You are an expert at extracting personal information, education, and skills from CV/Resume images.

Analyze the document image(s) and extract ONLY the following sections:

Personal Information:
- full_name: Complete name of the candidate
- email: Email address for contact
- phone: Phone number including country code if available (format as +84-xxxxxxxxx for Vietnamese numbers)
- location: Current city, state/province, and country
- graduation_year: Year of graduation from highest degree
- education_level: Highest level of education completed (must be one of: "highschool", "diploma", "associateDegree", "bachelor", "engineering", "master", "DoctorPhD")
- english_certificate: English proficiency certificates held (can include: "ielts", "toeic", "toefl")
- date_of_birth: Date of birth in any format mentioned
- gender: Gender identity if specified (must be one of: "female", "male", "notToSay")
- nationality: Country of citizenship
- linkedin_url: LinkedIn profile URL
- github_url: GitHub profile URL
- personal_website: Personal portfolio or website URL

Education Information:
- school: Name of the educational institution
- degree: Type of degree or diploma obtained
- field_of_study: Major, specialization, or field of study
- start_date: Start date of education in any format
- end_date: End date or expected graduation date
- gpa: Grade point average or academic score (do not include scale in gpa, only show gpa value). If GPA is on a 100-point scale, convert it to 10-point scale by dividing by 10.
- gpa_type: GPA scale system (must be one of: "4.0", "10.0", "5.0", "letterGrade", "classificationGrade", "germany"). Use "10.0" for converted 100-point scales.
- description: Additional details, coursework, or achievements

Skills Information:
- name: Name of the skill or technology
- category: Type of skill (must be either "Technical" for programming/tools or "Soft" for communication/leadership)

Guidelines:
- FOCUS ONLY on the sections mentioned above
- IGNORE work experience, projects, certifications, awards, publications, volunteer work, and other sections
- Extract only what is explicitly mentioned
- Don't invent or assume information
- Return null for missing fields
- Be precise with contact details
- For phone numbers: If the number appears to be Vietnamese (starts with 0, +84, or 84), format it as +84-xxxxxxxxx (remove leading 0 if present, add +84- prefix)
- For GPA: If a 100-point scale GPA is mentioned, convert it to 10-point scale by dividing by 10 and set gpa_type to "10.0\""""

    def get_output_model(self) -> type[BaseModel]:
        """Return the Pydantic model for structured output"""
        return PersonalInfoEducationAndSkills

    async def parse_from_text(self, text: str) -> PersonalInfoEducationAndSkills:
        """Parse personal info, education, and skills from text"""
        result = await self.parse_from_text_with_logging(text, self.get_parser_name())

        # Normalize phone number format
        if result.personal_info and result.personal_info.phone:
            result.personal_info.phone = await normalize_phone_number_async(
                result.personal_info.phone
            )
            logger.info(
                f"{self.get_parser_name()}: Normalized phone number to: {result.personal_info.phone}"
            )

        logger.info(
            f"{self.get_parser_name()}: Successfully extracted personal info, {len(result.education)} education entries, and {len(result.skills)} skills"
        )
        return result

    async def parse_from_images(
        self, image_base64_list: list[str], mime_type: str, filename: str
    ) -> PersonalInfoEducationAndSkills:
        """Parse personal info, education, and skills from document images"""
        result = await self.parse_from_images_with_logging(
            image_base64_list, mime_type, filename, self.get_parser_name()
        )

        # Normalize phone number format
        if result.personal_info and result.personal_info.phone:
            result.personal_info.phone = await normalize_phone_number_async(
                result.personal_info.phone
            )
            logger.info(
                f"{self.get_parser_name()}: Normalized phone number to: {result.personal_info.phone}"
            )

        return result
