import logging

from pydantic import BaseModel

from app.models import Project, WorkExperience
from app.parser.base import BaseParser

logger = logging.getLogger(__name__)


class WorkExperienceAndProjectsResult(BaseModel):
    """Result model for work experience and projects parsing"""

    work_experience: list[WorkExperience]
    projects: list[Project]


class WorkExperienceAndProjectsParser(BaseParser):
    """Combined parser for work experience and projects sections"""

    def get_text_prompt_messages(self) -> list[tuple[str, str]]:
        """Return the prompt messages for text-based parsing"""
        return [
            (
                "system",
                """You are an expert at extracting work experience and projects from CV/Resume text.

Extract BOTH sections: work experience and projects:

WORK EXPERIENCE:
- company: Name of the employer or company
- job_title: Position or job title held
- employment_type: Type of employment (full-time, part-time, contract, internship)
- location: Work location (city, state, country)
- start_date: Employment start date in any format
- end_date: Employment end date or 'Present' for current positions
- description: Job responsibilities, achievements, and key accomplishments

PROJECTS:
- name: Title or name of the project
- description: Detailed description of the project and its objectives
- role: Role or position in the project team
- start_date: Project start date in any format
- end_date: Project completion date or 'Ongoing' for current projects
- url: URL to project demo, repository, or documentation
- technologies: List of technologies, tools, or frameworks used

Guidelines:
- List work experience in reverse chronological order (most recent first)
- List projects in reverse chronological order (most recent first)
- Parse dates consistently (YYYY-MM format preferred, use YYYY if only year available)
- Extract complete and detailed descriptions for both work experience and projects
- Include all relevant work experience (full-time, part-time, internships, freelance)
- Include all projects (personal, academic, professional, open-source, freelance)
- For projects: extract specific technologies as a list, not as part of description
- Be comprehensive but accurate for both sections""",
            ),
            (
                "human",
                "Extract work experience and projects from this CV text:\n\n{cv_text}",
            ),
        ]

    def get_multimodal_prompt_text(self) -> str:
        """Return the prompt text for multimodal parsing"""
        return """You are an expert at extracting work experience and projects from CV/Resume images.

Analyze the document image(s) and extract ONLY the following sections:

WORK EXPERIENCE:
- company: Name of the employer or company
- job_title: Position or job title held
- employment_type: Type of employment (full-time, part-time, contract, internship)
- location: Work location (city, state, country)
- start_date: Employment start date in any format
- end_date: Employment end date or 'Present' for current positions
- description: Job responsibilities, achievements, and key accomplishments

PROJECTS:
- name: Title or name of the project
- description: Detailed description of the project and its objectives
- role: Role or position in the project team
- start_date: Project start date in any format
- end_date: Project completion date or 'Ongoing' for current projects
- url: URL to project demo, repository, or documentation
- technologies: List of technologies, tools, or frameworks used

Guidelines:
- FOCUS ONLY on work experience and projects sections
- IGNORE personal info, education, skills, certifications, awards, publications, and other sections
- List work experience in reverse chronological order (most recent first)
- List projects in reverse chronological order (most recent first)
- Parse dates consistently (YYYY-MM format preferred, use YYYY if only year available)
- Extract complete and detailed descriptions
- Include all relevant work experience (full-time, part-time, internships, freelance)
- Include all projects (personal, academic, professional, open-source, freelance)
- For projects: extract specific technologies as a list, not as part of description
- Be comprehensive but accurate for both sections"""

    def get_output_model(self) -> type[BaseModel]:
        """Return the Pydantic model for structured output"""
        return WorkExperienceAndProjectsResult

    async def parse_from_text(
        self, text: str
    ) -> tuple[list[WorkExperience], list[Project]]:
        """Parse work experience and projects from text"""
        result = await self.parse_from_text_with_logging(text, self.get_parser_name())

        logger.info(
            f"{self.get_parser_name()}: Successfully extracted {len(result.work_experience)} work experience entries and {len(result.projects)} projects"
        )
        return result.work_experience, result.projects

    async def parse_from_images(
        self, image_base64_list: list[str], mime_type: str, filename: str
    ) -> tuple[list[WorkExperience], list[Project]]:
        """Parse work experience and projects from document images"""
        result = await self.parse_from_images_with_logging(
            image_base64_list, mime_type, filename, self.get_parser_name()
        )

        logger.info(
            f"{self.get_parser_name()}: Successfully extracted {len(result.work_experience)} work experience entries and {len(result.projects)} projects"
        )
        return result.work_experience, result.projects
