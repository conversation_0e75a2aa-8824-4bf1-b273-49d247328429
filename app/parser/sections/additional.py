import logging
from typing import Any

from pydantic import BaseModel

from app.models import (
    Award,
    Certification,
    ExtracurricularActivity,
    Language,
    Publication,
    Reference,
    VolunteerExperience,
)
from app.parser.base import BaseParser

logger = logging.getLogger(__name__)


class AdditionalSectionsAndCertifications(BaseModel):
    """Result model for additional sections and certifications parsing"""

    languages: list[Language]
    volunteer_experience: list[VolunteerExperience]
    extracurricular_activities: list[ExtracurricularActivity]
    references: list[Reference]
    certifications: list[Certification]
    awards: list[Award]
    publications: list[Publication]
    summary: str | None = None


class AdditionalSectionsAndCertificationsParser(BaseParser):
    """<PERSON><PERSON><PERSON> specialized for additional sections and certifications (languages, volunteer work, certifications, awards, publications, etc.)"""

    def get_text_prompt_messages(self) -> list[tuple[str, str]]:
        """Return the prompt messages for text-based parsing"""
        return [
            (
                "system",
                """You are an expert at extracting additional sections, certifications, and professional summary from CV/Resume text.

Extract information for ALL of the following sections:

LANGUAGES:
- language: Name of the language
- proficiency: Proficiency level (native, fluent, intermediate, basic, beginner)

VOLUNTEER EXPERIENCE:
- organization: Name of the volunteer organization or cause
- role: Volunteer position or role performed
- start_date: Start date of volunteer work
- end_date: End date or 'Present' for ongoing volunteer work
- description: Activities performed and impact made through volunteering

EXTRACURRICULAR ACTIVITIES:
- name: Name of the activity, club, or organization
- position: Leadership role or position held
- start_date: Start date of participation
- end_date: End date or 'Present' for ongoing activities
- description: Activities performed and achievements in the organization

REFERENCES:
- name: Full name of the reference person
- position: Job title or position of the reference
- company: Company or organization where reference works
- contact_info: Email, phone, or other contact information

CERTIFICATIONS:
- name: Name or title of the certification
- issuer: Organization or institution that issued the certification
- issue_date: Date when certification was obtained
- expiration_date: Expiration date if applicable
- credential_id: Unique identifier or license number
- credential_url: URL to verify or view the certification

AWARDS:
- title: Title or name of the award
- issuer: Organization or institution that granted the award
- date: Date when the award was received
- description: Details about the award and reason for recognition

PUBLICATIONS:
- title: Title of the publication or paper
- publisher: Journal, conference, or publisher name
- date: Publication or presentation date
- description: Abstract or summary of the publication
- url: URL to access the publication

PROFESSIONAL SUMMARY:
- Extract any professional summary, objective, or career summary section
- This may appear as "Summary", "Professional Summary", "Career Objective", "About Me", etc.
- Field description: Objective, career highlights or personal statement
- Keep it concise and professional

Guidelines:
- Extract all relevant information from each section
- Parse dates consistently (YYYY-MM format preferred, use YYYY if only year available)
- Include proficiency levels for languages
- For volunteer work and activities, include detailed descriptions
- For certifications, be precise with names and issuing organizations
- Extract complete contact information for references
- For summary, capture the essence of their professional profile
- Be comprehensive but accurate for all sections""",
            ),
            (
                "human",
                "Extract additional sections, certifications, and professional summary (languages, volunteer work, activities, references, certifications, awards, publications, summary) from this CV text:\n\n{cv_text}",
            ),
        ]

    def get_multimodal_prompt_text(self) -> str:
        """Return the prompt text for multimodal parsing"""
        return """You are an expert at extracting additional sections, certifications, and professional summary from CV/Resume images.

Analyze the document image(s) and extract ONLY the following sections:

LANGUAGES:
- language: Name of the language
- proficiency: Proficiency level (native, fluent, intermediate, basic, beginner)

VOLUNTEER EXPERIENCE:
- organization: Name of the volunteer organization or cause
- role: Volunteer position or role performed
- start_date: Start date of volunteer work
- end_date: End date or 'Present' for ongoing volunteer work
- description: Activities performed and impact made through volunteering

EXTRACURRICULAR ACTIVITIES:
- name: Name of the activity, club, or organization
- position: Leadership role or position held
- start_date: Start date of participation
- end_date: End date or 'Present' for ongoing activities
- description: Activities performed and achievements in the organization

REFERENCES:
- name: Full name of the reference person
- position: Job title or position of the reference
- company: Company or organization where reference works
- contact_info: Email, phone, or other contact information

CERTIFICATIONS:
- name: Name or title of the certification
- issuer: Organization or institution that issued the certification
- issue_date: Date when certification was obtained
- expiration_date: Expiration date if applicable
- credential_id: Unique identifier or license number
- credential_url: URL to verify or view the certification

AWARDS:
- title: Title or name of the award
- issuer: Organization or institution that granted the award
- date: Date when the award was received
- description: Details about the award and reason for recognition

PUBLICATIONS:
- title: Title of the publication or paper
- publisher: Journal, conference, or publisher name
- date: Publication or presentation date
- description: Abstract or summary of the publication
- url: URL to access the publication

PROFESSIONAL SUMMARY:
- Extract any professional summary, objective, or career summary section
- This may appear as "Summary", "Professional Summary", "Career Objective", "About Me", etc.
- Field description: Objective, career highlights or personal statement
- Keep it concise and professional

Guidelines:
- FOCUS ONLY on the sections mentioned above
- IGNORE personal info, education, work experience, projects, and skills sections
- Extract all relevant information from each section
- Parse dates consistently (YYYY-MM format preferred, use YYYY if only year available)
- Include proficiency levels for languages
- For volunteer work and activities, include detailed descriptions
- For certifications, be precise with names and issuing organizations
- Extract complete contact information for references
- For summary, capture the essence of their professional profile
- Be comprehensive but accurate for all sections"""

    def get_output_model(self) -> type[BaseModel]:
        """Return the Pydantic model for structured output"""
        return AdditionalSectionsAndCertifications

    async def parse_from_text(self, text: str) -> dict[str, Any]:
        """Parse additional sections and certifications from text"""
        result = await self.parse_from_text_with_logging(text, self.get_parser_name())

        logger.info(
            f"{self.get_parser_name()}: Successfully extracted {len(result.languages)} languages, "
            f"{len(result.volunteer_experience)} volunteer experiences, "
            f"{len(result.extracurricular_activities)} activities, {len(result.references)} references, "
            f"{len(result.certifications)} certifications, {len(result.awards)} awards, {len(result.publications)} publications, "
            f"and summary {'(present)' if result.summary else '(not found)'}"
        )

        return {
            "languages": result.languages,
            "volunteer_experience": result.volunteer_experience,
            "extracurricular_activities": result.extracurricular_activities,
            "references": result.references,
            "certifications": result.certifications,
            "awards": result.awards,
            "publications": result.publications,
            "summary": result.summary,
        }

    async def parse_from_images(
        self, image_base64_list: list[str], mime_type: str, filename: str
    ) -> dict[str, Any]:
        """Parse additional sections and certifications from document images"""
        result = await self.parse_from_images_with_logging(
            image_base64_list, mime_type, filename, self.get_parser_name()
        )

        logger.info(
            f"{self.get_parser_name()}: Successfully extracted {len(result.languages)} languages, "
            f"{len(result.volunteer_experience)} volunteer experiences, "
            f"{len(result.extracurricular_activities)} activities, {len(result.references)} references, "
            f"{len(result.certifications)} certifications, {len(result.awards)} awards, {len(result.publications)} publications, "
            f"and summary {'(present)' if result.summary else '(not found)'}"
        )

        return {
            "languages": result.languages,
            "volunteer_experience": result.volunteer_experience,
            "extracurricular_activities": result.extracurricular_activities,
            "references": result.references,
            "certifications": result.certifications,
            "awards": result.awards,
            "publications": result.publications,
            "summary": result.summary,
        }
