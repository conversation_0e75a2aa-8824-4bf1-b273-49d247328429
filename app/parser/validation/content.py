import logging
import time
from typing import Any

from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema.runnable import RunnablePassthrough
from langchain_openai import Chat<PERSON>penAI
from pydantic import BaseModel

from app.config import settings

logger = logging.getLogger(__name__)


class ContentValidationResult(BaseModel):
    """Result model for content validation"""

    is_cv_content: bool
    confidence: float
    reasoning: str


class ContentValidationParser:
    """Parser to validate if input content appears to be a CV/Resume"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=0.1,  # Lower temperature for more consistent validation
            api_key=settings.OPENAI_API_KEY,
        )

        self.prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert document classifier specializing in identifying CV/Resume content.

Analyze the provided text and determine if it appears to be a CV, Resume, or professional profile document.

Look for typical CV/Resume indicators:
- Personal contact information (name, email, phone, address)
- Professional sections (work experience, education, skills)
- Career-related content (job titles, companies, dates of employment)
- Educational background (schools, degrees, graduation dates)
- Professional skills or competencies
- Career objectives or professional summaries
- Professional achievements, projects, or certifications

Be FLEXIBLE and INCLUSIVE in your assessment:
- Accept various CV/Resume formats (traditional, modern, creative, academic, etc.)
- Accept international formats and styles
- Accept incomplete or draft versions
- Accept CVs in different languages
- Accept professional profiles or LinkedIn-style content
- Accept academic CVs, portfolios, or professional bios

REJECT clearly non-CV content:
- Random text, articles, or stories
- Code files or technical documentation (unless it's a developer's portfolio)
- Legal documents, contracts, or forms
- Marketing materials or advertisements
- Pure academic papers (unless part of an academic CV)
- Nonsense text or spam

Provide your assessment as:
- is_cv_content: true/false
- confidence: 0.0-1.0 (how confident you are in your assessment)
- reasoning: brief explanation of your decision

Be generous in accepting borderline cases - err on the side of inclusion rather than exclusion.""",
                ),
                (
                    "human",
                    "Analyze this text and determine if it appears to be CV/Resume content:\n\n{content}",
                ),
            ]
        )

    def create_chain(self):
        """Create the validation chain"""
        return (
            {"content": RunnablePassthrough()}
            | self.prompt
            | self.llm.with_structured_output(ContentValidationResult)
        )

    async def validate_content(self, text: str) -> dict[str, Any]:
        """Validate if the provided text appears to be CV/Resume content"""
        try:
            logger.debug("ContentValidationParser: Starting content validation")

            # For very short text, be more lenient
            if len(text.strip()) < 100:
                logger.debug("Content is very short, being lenient in validation")
                return {
                    "is_cv_content": True,
                    "confidence": 0.5,
                    "reasoning": "Content too short for reliable classification, allowing processing",
                }

            # Truncate very long text for validation (keep first 5000 chars)
            validation_text = text[:5000] if len(text) > 5000 else text
            start_time = time.time()
            chain = self.create_chain()
            result = await chain.ainvoke(validation_text)

            logger.debug(
                f"ContentValidationParser: Validation complete - is_cv: {result.is_cv_content}, "
                f"confidence: {result.confidence:.2f}"
            )
            end_time = time.time()
            logger.debug(
                f"ContentValidationParser: Validation took {end_time - start_time:.2f} seconds"
            )
            return {
                "is_cv_content": result.is_cv_content,
                "confidence": result.confidence,
                "reasoning": result.reasoning,
            }
        except Exception as e:
            logger.error(
                f"ContentValidationParser: Error during validation: {str(e)}",
                exc_info=True,
            )
            # On validation error, be permissive and allow processing
            return {
                "is_cv_content": True,
                "confidence": 0.5,
                "reasoning": f"Validation error occurred, allowing processing: {str(e)}",
            }
