import asyncio
import base64
import logging
import time

from langchain.prompts import Chat<PERSON>romptTemplate
from langchain.schema import HumanMessage
from langchain.schema.runnable import RunnablePassthrough
from langchain_community.callbacks import get_openai_callback
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>

from app.config import settings
from app.models import CVData

logger = logging.getLogger(__name__)


class CVParser:
    """CV Parser using LangChain with structured output"""

    def __init__(self):
        self.llm = ChatOpenAI(
            model=settings.OPENAI_MODEL,
            temperature=settings.TEMPERATURE,
            api_key=settings.OPENAI_API_KEY,
        )

        # Create prompt template for text-based parsing
        self.text_prompt = ChatPromptTemplate.from_messages(
            [
                (
                    "system",
                    """You are an expert CV/Resume parser. Extract information from the provided CV text and structure it according to the given schema.

Guidelines:
- Extract all relevant information accurately
- If information is not available, leave the field as null
- Parse dates in a consistent format when possible
- Categorize skills appropriately
- Extract complete descriptions for work experience and projects
- Be thorough but accurate - don't invent information""",
                ),
                ("human", "Parse the following CV/Resume text:\n\n{cv_text}"),
            ]
        )

        # Create prompt template for multimodal parsing
        self.multimodal_prompt_text = """You are an expert CV/Resume parser. Analyze the provided document image and extract information according to the given schema.

Guidelines:
- Extract all relevant information accurately from the visual document
- If information is not available, leave the field as null
- Parse dates in a consistent format when possible
- Categorize skills appropriately
- Extract complete descriptions for work experience and projects
- Be thorough but accurate - don't invent information

Analyze the attached CV/Resume document and extract all information."""

    def create_text_chain(self):
        """Create chain for text-based parsing"""
        return (
            {"cv_text": RunnablePassthrough()}
            | self.text_prompt
            | self.llm.with_structured_output(CVData)
        )

    def create_multimodal_chain(self):
        """Create chain for multimodal parsing"""
        return self.llm.with_structured_output(CVData)

    async def parse_from_text(self, text: str, filename: str) -> CVData:
        """Parse CV from extracted text"""
        try:
            logger.info(f"Starting text-based CV parsing for file: {filename}")
            logger.debug(f"Text length: {len(text)} characters")

            chain = self.create_text_chain()
            parsed_data = await chain.ainvoke(text)

            logger.info(f"Successfully parsed CV text for file: {filename}")

            return parsed_data
        except Exception as e:
            logger.error(
                f"Error parsing CV text for file {filename}: {str(e)}", exc_info=True
            )
            raise Exception(f"Error parsing CV text: {str(e)}")

    async def parse_from_file(
        self, file_base64: str, mime_type: str, filename: str
    ) -> CVData:
        """Parse CV directly from file using multimodal capabilities"""
        try:
            logger.info(f"Starting multimodal CV parsing for file: {filename}")
            logger.debug(
                f"MIME type: {mime_type}, base64 length: {len(file_base64)} characters"
            )

            # Handle PDF files - convert to images first
            if mime_type == "application/pdf":
                from app.file_processors import PDFToImageConverter

                logger.info(
                    f"Converting PDF to images for multimodal processing: {filename}"
                )

                # Decode base64 to bytes (CPU-intensive operation in thread pool)
                pdf_bytes = await asyncio.to_thread(base64.b64decode, file_base64)

                # Convert PDF to images (now async)
                image_base64_list = await PDFToImageConverter.convert_pdf_to_images(
                    pdf_bytes, filename
                )

                # Create message content with text prompt and all images
                content = [{"type": "text", "text": self.multimodal_prompt_text}]

                # Add each image to the content
                image_format = settings.PDF_IMAGE_FORMAT.lower()
                mime_type_image = f"image/{image_format}"

                for i, img_base64 in enumerate(image_base64_list):
                    content.append(
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{mime_type_image};base64,{img_base64}"
                            },
                        }
                    )
                    logger.debug(f"Added page {i + 1} to multimodal request")

                logger.info(
                    f"Sending {len(image_base64_list)} images to OpenAI Vision API for {filename}"
                )

            else:
                # Non-PDF files - send directly (for future support of other image formats)
                content = [
                    {"type": "text", "text": self.multimodal_prompt_text},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:{mime_type};base64,{file_base64}"},
                    },
                ]
                logger.info(
                    f"Sending {mime_type} file directly to OpenAI Vision API for {filename}"
                )

            # Create message with content
            messages = [HumanMessage(content=content)]

            logger.debug("Sending request to OpenAI Vision API")
            chain = self.create_multimodal_chain()

            # Track token usage for multimodal API call
            start_time = time.time()
            with get_openai_callback() as cb:
                parsed_data = await chain.ainvoke(messages)

            # Log comprehensive token usage information
            processing_time = time.time() - start_time
            logger.info(
                f"CVParser (Full): Token usage - Input: {cb.prompt_tokens:,}, "
                f"Output: {cb.completion_tokens:,}, Total: {cb.total_tokens:,}, "
                f"Cost: ${cb.total_cost:.4f}, Time: {processing_time:.1f}s"
            )
            logger.info(f"Successfully parsed CV with multimodal for file: {filename}")

            return parsed_data
        except Exception as e:
            logger.error(
                f"Error parsing CV with multimodal for file {filename}: {str(e)}",
                exc_info=True,
            )
            # Log specific OpenAI API errors
            if "400" in str(e):
                logger.error(
                    f"OpenAI API 400 Bad Request for {filename} - possible invalid base64 or rate limit"
                )
            elif "429" in str(e):
                logger.error(f"OpenAI API 429 Rate Limit exceeded for {filename}")
            elif "401" in str(e):
                logger.error(
                    f"OpenAI API 401 Unauthorized for {filename} - check API key"
                )
            elif "500" in str(e):
                logger.error(f"OpenAI API 500 Internal Server Error for {filename}")

            raise Exception(f"Error parsing CV with multimodal: {str(e)}")
