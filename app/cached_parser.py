import logging
import time
from typing import Any

from app.cache import cache_service
from app.parser import C<PERSON>arserState, cv_parser_workflow

logger = logging.getLogger(__name__)


class CachedCVParserWorkflow:
    """Cache wrapper for CV parser workflow that handles caching logic"""

    def __init__(self):
        pass

    async def ainvoke(self, initial_state: CVParserState) -> dict[str, Any]:
        """
        Execute CV parser workflow with caching support

        Args:
            initial_state: CVParserState for the workflow

        Returns:
            dict containing the parsed result, same as cv_parser_workflow.ainvoke
        """
        # Generate cache key
        cache_key = cache_service.generate_cache_key(initial_state)

        # Try to get cached result
        cached_result = await self._get_from_cache(cache_key)
        if cached_result is not None:
            logger.info(
                f"Cache HIT for {initial_state.get('filename', 'unknown')} (key: {cache_key})"
            )
            return cached_result

        # Cache miss - execute the actual workflow
        logger.info(
            f"Cache MISS for {initial_state.get('filename', 'unknown')} (key: {cache_key})"
        )

        start_time = time.time()
        try:
            # Execute the original workflow
            result = await cv_parser_workflow.ainvoke(initial_state)

            execution_time = time.time() - start_time
            logger.info(
                f"Workflow execution completed in {execution_time:.2f}s for {initial_state.get('filename', 'unknown')}"
            )

            # Cache the result only if it's not an error
            is_error = bool(result.get("errors") or result.get("error"))
            if not is_error:
                await self._store_in_cache(cache_key, result, is_error=False)

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(
                f"Workflow execution failed after {execution_time:.2f}s for {initial_state.get('filename', 'unknown')}: {str(e)}"
            )

            # Re-raise the exception to maintain original behavior (do not cache errors)
            raise

    async def _get_from_cache(self, cache_key: str) -> dict[str, Any] | None:
        """Get result from cache with error handling"""
        try:
            cached_data = await cache_service.get_cached_result(cache_key)
            if cached_data is None:
                return None

            # Validate that cached data has the expected structure
            if not isinstance(cached_data, dict):
                logger.warning(
                    f"Invalid cached data structure for key {cache_key}, skipping cache"
                )
                return None

            # Reconstruct CVData if present
            if "parsed_data" in cached_data and cached_data["parsed_data"]:
                # Try to deserialize CVData from the cached data
                cv_data = cache_service.deserialize_cvdata(cached_data)
                if cv_data:
                    cached_data["parsed_data"] = cv_data
                else:
                    logger.warning(
                        f"Failed to deserialize CVData from cache for key {cache_key}"
                    )
                    return None

            return cached_data

        except Exception as e:
            logger.error(f"Error retrieving from cache for key {cache_key}: {str(e)}")
            return None

    async def _store_in_cache(
        self, cache_key: str, result: dict[str, Any], is_error: bool = False
    ) -> bool:
        """Store result in cache with error handling"""
        try:
            # Create a serializable copy of the result
            cache_data = result.copy()

            # Serialize CVData to dict if present
            if "parsed_data" in cache_data and cache_data["parsed_data"]:
                parsed_data = cache_data["parsed_data"]
                if hasattr(parsed_data, "model_dump"):
                    cache_data["parsed_data"] = parsed_data.model_dump()
                elif hasattr(parsed_data, "dict"):
                    cache_data["parsed_data"] = parsed_data.dict()

            success = await cache_service.cache_result(cache_key, cache_data, is_error)
            if not success:
                logger.warning(f"Failed to store result in cache for key {cache_key}")

            return success

        except Exception as e:
            logger.error(f"Error storing in cache for key {cache_key}: {str(e)}")
            return False


# Global instance of the cached workflow
cached_cv_parser_workflow = CachedCVParserWorkflow()
