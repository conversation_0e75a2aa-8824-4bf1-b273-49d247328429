from typing import List, Literal, Optional

from pydantic import BaseModel, Field


class PersonalInfo(BaseModel):
    """Personal and contact information of the candidate"""

    full_name: Optional[str] = Field(None, description="Complete name of the candidate")
    email: Optional[str] = Field(None, description="Email address for contact")
    phone: Optional[str] = Field(
        None, description="Phone number including country code if available"
    )
    location: Optional[str] = Field(
        None, description="Current city, state/province, and country"
    )
    graduation_year: Optional[str] = Field(
        None, description="Year of graduation from highest degree"
    )
    education_level: Literal[
        None,
        "highschool",
        "diploma",
        "associateDegree",
        "bachelor",
        "engineering",
        "master",
        "DoctorPhD",
    ] = Field(None, description="Highest level of education completed")
    english_certificate: Optional[List[Literal["ielts", "toeic", "toefl"]]] = Field(
        None, description="English proficiency certificates held"
    )
    date_of_birth: Optional[str] = Field(
        None, description="Date of birth in any format mentioned"
    )
    gender: Literal["female", "male", "notToSay"] = Field(
        None, description="Gender identity if specified"
    )
    nationality: Optional[str] = Field(None, description="Country of citizenship")
    linkedin_url: Optional[str] = Field(None, description="LinkedIn profile URL")
    github_url: Optional[str] = Field(None, description="GitHub profile URL")
    personal_website: Optional[str] = Field(
        None, description="Personal portfolio or website URL"
    )


class Education(BaseModel):
    """Educational background including schools, degrees, and academic achievements"""

    school: Optional[str] = Field(
        None, description="Name of the educational institution"
    )
    degree: Optional[str] = Field(
        None, description="Type of degree or diploma obtained"
    )
    field_of_study: Optional[str] = Field(
        None, description="Major, specialization, or field of study"
    )
    start_date: Optional[str] = Field(
        None, description="Start date of education in any format"
    )
    end_date: Optional[str] = Field(
        None, description="End date or expected graduation date"
    )
    gpa: Optional[str] = Field(
        None,
        description="Grade point average or academic score. Do not including scale in gpa. Only show gpa.",
    )
    gpa_type: Optional[
        Literal["4.0", "10.0", "5.0", "letterGrade", "classificationGrade", "germany"]
    ] = Field(
        None,
        description="GPA type (e.g., '4.0 scale', '10.0 scale', '100 point scale')",
    )
    description: Optional[str] = Field(
        None, description="Additional details, coursework, or achievements"
    )


class WorkExperience(BaseModel):
    """Professional work experience and employment history"""

    company: Optional[str] = Field(None, description="Name of the employer or company")
    job_title: Optional[str] = Field(None, description="Position or job title held")
    employment_type: Optional[str] = Field(
        None,
        description="Type of employment (full-time, part-time, contract, internship)",
    )
    location: Optional[str] = Field(
        None, description="Work location (city, state, country)"
    )
    start_date: Optional[str] = Field(
        None, description="Employment start date in any format"
    )
    end_date: Optional[str] = Field(
        None, description="Employment end date or 'Present' for current positions"
    )
    description: Optional[str] = Field(
        None, description="Job responsibilities, achievements, and key accomplishments"
    )


class Skill(BaseModel):
    """Technical and soft skills possessed by the candidate"""

    name: Optional[str] = Field(None, description="Name of the skill or technology")
    category: Literal["Technical", "Soft"] = Field(
        None,
        description="Type of skill: Technical (programming, tools) or Soft (communication, leadership)",
    )


class Project(BaseModel):
    """Personal or professional projects undertaken by the candidate"""

    name: Optional[str] = Field(None, description="Title or name of the project")
    description: Optional[str] = Field(
        None, description="Detailed description of the project and its objectives"
    )
    role: Optional[str] = Field(
        None, description="Role or position in the project team"
    )
    start_date: Optional[str] = Field(
        None, description="Project start date in any format"
    )
    end_date: Optional[str] = Field(
        None, description="Project completion date or 'Ongoing' for current projects"
    )
    url: Optional[str] = Field(
        None, description="URL to project demo, repository, or documentation"
    )
    technologies: list[str] = Field(
        default_factory=list,
        description="List of technologies, tools, or frameworks used",
    )


class Certification(BaseModel):
    """Professional certifications and credentials earned"""

    name: Optional[str] = Field(None, description="Name or title of the certification")
    issuer: Optional[str] = Field(
        None, description="Organization or institution that issued the certification"
    )
    issue_date: Optional[str] = Field(
        None, description="Date when certification was obtained"
    )
    expiration_date: Optional[str] = Field(
        None, description="Expiration date if applicable"
    )
    credential_id: Optional[str] = Field(
        None, description="Unique identifier or license number"
    )
    credential_url: Optional[str] = Field(
        None, description="URL to verify or view the certification"
    )


class Award(BaseModel):
    """Awards, honors, and recognition received"""

    title: Optional[str] = Field(None, description="Title or name of the award")
    issuer: Optional[str] = Field(
        None, description="Organization or institution that granted the award"
    )
    date: Optional[str] = Field(None, description="Date when the award was received")
    description: Optional[str] = Field(
        None, description="Details about the award and reason for recognition"
    )


class Language(BaseModel):
    """Language skills and proficiency levels"""

    language: Optional[str] = Field(None, description="Name of the language")
    proficiency: Optional[str] = Field(
        None,
        description="Proficiency level (native, fluent, intermediate, basic, beginner)",
    )


class Publication(BaseModel):
    """Research publications, papers, and written works"""

    title: Optional[str] = Field(None, description="Title of the publication or paper")
    publisher: Optional[str] = Field(
        None, description="Journal, conference, or publisher name"
    )
    date: Optional[str] = Field(None, description="Publication or presentation date")
    description: Optional[str] = Field(
        None, description="Abstract or summary of the publication"
    )
    url: Optional[str] = Field(None, description="URL to access the publication")


class Reference(BaseModel):
    """Professional references who can vouch for the candidate"""

    name: Optional[str] = Field(None, description="Full name of the reference person")
    position: Optional[str] = Field(
        None, description="Job title or position of the reference"
    )
    company: Optional[str] = Field(
        None, description="Company or organization where reference works"
    )
    contact_info: Optional[str] = Field(
        None, description="Email, phone, or other contact information"
    )


class VolunteerExperience(BaseModel):
    """Volunteer work and community service experience"""

    organization: Optional[str] = Field(
        None, description="Name of the volunteer organization or cause"
    )
    role: Optional[str] = Field(
        None, description="Volunteer position or role performed"
    )
    start_date: Optional[str] = Field(None, description="Start date of volunteer work")
    end_date: Optional[str] = Field(
        None, description="End date or 'Present' for ongoing volunteer work"
    )
    description: Optional[str] = Field(
        None, description="Activities performed and impact made through volunteering"
    )


class ExtracurricularActivity(BaseModel):
    """Extracurricular activities, clubs, and organizations"""

    name: Optional[str] = Field(
        None, description="Name of the activity, club, or organization"
    )
    position: Optional[str] = Field(
        None, description="Leadership role or position held"
    )
    start_date: Optional[str] = Field(None, description="Start date of participation")
    end_date: Optional[str] = Field(
        None, description="End date or 'Present' for ongoing activities"
    )
    description: Optional[str] = Field(
        None, description="Activities performed and achievements in the organization"
    )


class CVData(BaseModel):
    """Complete structured data extracted from a CV/Resume"""

    personal_info: Optional[PersonalInfo] = Field(
        None, description="Personal contact and demographic information"
    )
    summary: Optional[str] = Field(
        None, description="Objective, career highlights or personal statement"
    )
    education: list[Education] = Field(
        default_factory=list,
        description="Educational background and academic achievements",
    )
    work_experience: list[WorkExperience] = Field(
        default_factory=list,
        description="Professional work experience and employment history",
    )
    skills: list[Skill] = Field(
        default_factory=list, description="Technical and soft skills"
    )
    projects: list[Project] = Field(
        default_factory=list, description="Personal and professional projects"
    )
    certifications: list[Certification] = Field(
        default_factory=list, description="Professional certifications and credentials"
    )
    awards: list[Award] = Field(
        default_factory=list, description="Awards, honors, and recognition received"
    )
    languages: list[Language] = Field(
        default_factory=list, description="Language skills and proficiency levels"
    )
    publications: list[Publication] = Field(
        default_factory=list, description="Research publications and written works"
    )
    references: list[Reference] = Field(
        default_factory=list, description="Professional references"
    )
    volunteer_experience: list[VolunteerExperience] = Field(
        default_factory=list, description="Volunteer work and community service"
    )
    extracurricular_activities: list[ExtracurricularActivity] = Field(
        default_factory=list,
        description="Clubs, organizations, and extracurricular involvement",
    )


class ParseRequest(BaseModel):
    """Request model for parsing raw text"""

    text: str = Field(..., description="Raw CV/Resume text to parse")
    filename: str = Field(
        default="raw_text_input.txt", description="Optional filename for metadata"
    )
    multimodal: bool = Field(
        default=False,
        description="Use multimodal vision parsing (only for file uploads)",
    )


class CVParseResponse(BaseModel):
    """Response model for CV parsing"""

    request_id: str
    status: str
    success: bool
    profile: dict
    meta_data: dict
