import logging
import uuid
from contextlib import asynccontextmanager
from datetime import datetime

from fastapi import (
    BackgroundTasks,
    FastAPI,
    File,
    Form,
    HTTPException,
    Request,
    UploadFile,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import HttpUrl

from app.cache import initialize_cache, shutdown_cache
from app.cached_parser import cached_cv_parser_workflow
from app.callback import CallbackPayload, process_cv_with_callback
from app.config import settings
from app.file_processors import process_file_with_fallback
from app.models import CVParseResponse, ParseRequest
from app.parser import CVParserState

# Set up logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events"""
    # Startup
    try:
        await initialize_cache()
        logger.info("Cache initialization completed")
    except Exception as e:
        logger.warning(
            f"Cache initialization failed, continuing without cache: {str(e)}"
        )
        # Don't let cache failure prevent API startup

    yield

    # Shutdown
    try:
        await shutdown_cache()
        logger.info("Cache shutdown completed")
    except Exception as e:
        logger.warning(f"Cache shutdown failed: {str(e)}")
        # Don't let cache shutdown failure block app shutdown


# Create FastAPI app with lifespan
app = FastAPI(
    title=settings.API_TITLE,
    version=settings.API_VERSION,
    description=settings.API_DESCRIPTION,
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "CV Parser API",
        "version": settings.API_VERSION,
        "endpoints": {
            "parse": "/internal/v1/parse",
            "health": "/health",
            "mock_callback": "/mock/callback",
        },
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "version": settings.API_VERSION,
        "model": settings.OPENAI_MODEL,
    }


@app.post("/mock/callback")
async def mock_callback_endpoint(payload: CallbackPayload):
    """
    Mock callback endpoint for testing CV parser callbacks.

    This endpoint simulates a webhook receiver that would process
    CV parsing results from the parser.

    Use this URL as the callback_url when testing:
    http://localhost:8080/mock/callback
    """

    logger.info(f"Mock callback received for file: {payload.original_filename}")
    logger.info(f"Status: {payload.status}")
    logger.info(f"Success: {payload.success}")

    if payload.success:
        logger.info(f"Received mock callback payload: {payload.model_dump()}")
    else:
        logger.error(
            f"CV parsing failed: {payload.meta_data.get('error', 'Unknown error')}"
        )

    # Return success response
    import datetime

    return {
        "status": "received",
        "message": f"Mock callback processed for {payload.original_filename}",
        "received_at": datetime.datetime.now().isoformat(),
    }


@app.post("/internal/v1/parse", response_model=CVParseResponse)
async def parse_cv(
    request: Request,
    background_tasks: BackgroundTasks,
    file: UploadFile | None = File(
        None, description="CV file to parse (PDF, DOC, or DOCX)"
    ),
    multimodal: bool = Form(
        False, description="Use multimodal vision parsing for files"
    ),
    callback_url: HttpUrl | None = Form(
        None, description="Optional callback URL to receive results asynchronously"
    ),
) -> CVParseResponse:
    """
    Parse CV/Resume using text extraction or multimodal vision parsing

    This endpoint accepts:
    1. File upload (PDF, DOC, or DOCX) with text extraction (default)
    2. File upload with multimodal vision parsing (set multimodal=true)
    3. Raw text in JSON body - parses directly

    Response format:
    Always returns CVParseResponse format with structured CV data.
    If callback_url is provided, processing is done asynchronously.

    Args:
        file: CV file to parse
        multimodal: Use multimodal vision parsing for files
        callback_url: Optional URL to receive parsing results asynchronously

    Returns:
        CVParseResponse with structured CV data
    """
    try:
        # Handle JSON request body for text parsing
        request_body = None
        if not file:
            # Try to parse JSON body
            content_type = request.headers.get("content-type", "").lower()
            if "application/json" in content_type:
                try:
                    body_bytes = await request.body()
                    if body_bytes:
                        import json

                        # Check if the body looks like binary data (e.g., a DOC file sent as body)
                        # This prevents trying to decode binary DOC files as UTF-8
                        try:
                            body_text = body_bytes.decode("utf-8")
                        except UnicodeDecodeError:
                            logger.error(
                                "Request body contains binary data, expected JSON text"
                            )
                            raise HTTPException(
                                status_code=400,
                                detail="Request body contains binary data. Please use file upload for DOC/PDF files.",
                            )

                        body_dict = json.loads(body_text)
                        request_body = ParseRequest(**body_dict)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON body: {e}")
                    raise HTTPException(status_code=422, detail="Invalid JSON format")
                except HTTPException:
                    # Re-raise HTTPExceptions
                    raise
                except Exception as e:
                    logger.error(f"Failed to parse request body: {e}")
                    raise HTTPException(
                        status_code=400, detail="Invalid request body format"
                    )

        # Validate that at least one input is provided
        if not file and not request_body:
            raise HTTPException(
                status_code=400,
                detail="Either file upload or text in request body must be provided",
            )

        # Determine parsing mode
        use_multimodal = False
        if file:
            # File upload mode - check multimodal flag
            use_multimodal = multimodal
        elif request_body:
            # JSON mode - check multimodal in request body (only applies to future file uploads)
            use_multimodal = request_body.multimodal and file is not None

        # Check multimodal settings
        if use_multimodal and not settings.ENABLE_MULTIMODAL:
            raise HTTPException(
                status_code=403,
                detail="Multimodal parsing is disabled in configuration",
            )

        # Process input based on mode
        if file and use_multimodal:
            # Forced multimodal file processing
            logger.info(f"Received file for forced multimodal parsing: {file.filename}")

            # Use fallback function with forced multimodal mode
            fallback_result = await process_file_with_fallback(
                file,
                settings.ALLOWED_EXTENSIONS,
                settings.MAX_FILE_SIZE,
                force_multimodal=True,
            )

            logger.info(
                f"Converted file to base64 for multimodal, mode: {fallback_result.mode_used}"
            )

            # Create initial state for multimodal
            initial_state: CVParserState = {
                "input_text": None,
                "input_file_base64": fallback_result.file_base64,
                "filename": fallback_result.filename,
                "parsed_data": None,
                "error": None,
                "mode": "multimodal",
                "fallback_triggered": False,
                "fallback_reason": "Forced multimodal mode",
                "original_extraction_method": None,
            }

        elif file:
            # Automatic file processing with fallback support
            logger.info(f"Received file: {file.filename}")

            # Use the new fallback-enabled file processing
            fallback_result = await process_file_with_fallback(
                file, settings.ALLOWED_EXTENSIONS, settings.MAX_FILE_SIZE
            )

            if fallback_result.fallback_triggered:
                logger.info(
                    f"Fallback triggered for {fallback_result.filename}: {fallback_result.fallback_reason}"
                )

            logger.info(
                f"File processed using {fallback_result.mode_used} mode for {fallback_result.filename}"
            )

            if fallback_result.mode_used == "text":
                # Text extraction was successful
                logger.info(
                    f"Extracted {len(fallback_result.text)} characters from {fallback_result.filename}"
                )

                # Create initial state for text
                initial_state: CVParserState = {
                    "input_text": fallback_result.text,
                    "input_file_base64": None,
                    "filename": fallback_result.filename,
                    "parsed_data": None,
                    "error": None,
                    "mode": "text",
                    "fallback_triggered": False,
                    "fallback_reason": None,
                    "original_extraction_method": None,  # Will be set by file processor
                }
            else:
                # Fallback to multimodal was triggered
                logger.info(f"Using multimodal fallback for {fallback_result.filename}")

                # Create initial state for multimodal
                initial_state: CVParserState = {
                    "input_text": None,
                    "input_file_base64": fallback_result.file_base64,
                    "filename": fallback_result.filename,
                    "parsed_data": None,
                    "error": None,
                    "mode": "multimodal",
                    "fallback_triggered": True,
                    "fallback_reason": fallback_result.fallback_reason,
                    "original_extraction_method": None,  # Could be extracted from fallback_reason
                }

        else:
            # Raw text processing
            logger.info("Received raw text input")
            text = request_body.text
            filename = request_body.filename or "raw_text_input.txt"
            logger.info(f"Received {len(text)} characters of raw text")

            # Create initial state for text
            initial_state: CVParserState = {
                "input_text": text,
                "input_file_base64": None,
                "filename": filename,
                "parsed_data": None,
                "error": None,
                "mode": "text",
                "fallback_triggered": False,
                "fallback_reason": None,
                "original_extraction_method": "raw_text_input",
            }

        # Generate unique request ID for tracking
        request_id = str(uuid.uuid4())
        filename = initial_state.get("filename", "unknown")

        # Check if callback mode is requested
        if callback_url:
            logger.info(
                f"[{request_id}] Processing CV with callback for file: {filename} in mode: {initial_state.get('mode')}"
            )

            # Start background task for async processing
            background_tasks.add_task(
                process_cv_with_callback,
                initial_state,
                str(callback_url),
                request_id,
                filename,
            )

            # Return immediate response
            return CVParseResponse(
                request_id=request_id,
                status="processing",
                success=True,
                profile={},
                meta_data={
                    "cv_text": "",
                    "parser_time": datetime.now().isoformat(),
                },
            )

        else:
            # Synchronous processing
            logger.info(
                f"Starting CV parsing workflow for file: {filename} in mode: {initial_state.get('mode')}"
            )
            result = await cached_cv_parser_workflow.ainvoke(initial_state)

            # Check for errors
            if result.get("errors") and result["errors"]:
                error_msg = "; ".join(result["errors"])
                logger.error(f"Parsing errors: {error_msg}")
                raise HTTPException(
                    status_code=400, detail=f"Parsing failed: {error_msg}"
                )

            if result.get("error"):
                logger.error(f"Parsing error: {result['error']}")
                raise HTTPException(status_code=400, detail=result["error"])

            # Return parsed data
            parsed_data = result.get("parsed_data")
            if not parsed_data:
                raise HTTPException(status_code=500, detail="Failed to parse CV data")

            # Determine the actual mode used and fallback status
            actual_mode = result.get("mode", initial_state.get("mode", "unknown"))
            fallback_triggered = initial_state.get("fallback_triggered", False)
            fallback_reason = initial_state.get("fallback_reason")

            mode_description = f"{actual_mode}"
            if fallback_triggered:
                mode_description += (
                    f" (fallback from text extraction: {fallback_reason})"
                )
            elif use_multimodal:
                mode_description += " (forced multimodal)"

            candidate_name = (
                parsed_data.personal_info.full_name
                if parsed_data.personal_info
                else "Unknown"
            )
            logger.info(
                f"Successfully parsed CV ({mode_description}) for: {candidate_name}"
            )

            # Log fallback statistics if applicable
            if fallback_triggered:
                logger.info(
                    f"FALLBACK SUCCESS: {initial_state['filename']} processed via multimodal after text extraction failed: {fallback_reason}"
                )

            return CVParseResponse(
                request_id=request_id,
                status="succeeded",
                success=True,
                profile=parsed_data.model_dump(),
                meta_data={
                    "cv_text": "",
                    "parser_time": datetime.now().isoformat(),
                },
            )

    except HTTPException:
        raise
    except Exception as e:
        # If it's a Pydantic validation error, let FastAPI handle it properly (422)
        from pydantic import ValidationError

        if isinstance(e, ValidationError):
            raise e  # Let FastAPI handle this as 422
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Custom HTTP exception handler"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail, "status_code": exc.status_code},
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """General exception handler"""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500, content={"error": "Internal server error", "status_code": 500}
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8080)
