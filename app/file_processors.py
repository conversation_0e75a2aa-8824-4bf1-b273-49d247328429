import asyncio
import base64
import io
import logging
import os
import re
import subprocess
import tempfile
from pathlib import Path
from typing import Named<PERSON><PERSON>le

import textract
from fastapi import HTTPEx<PERSON>, UploadFile
from pdf2image import convert_from_bytes

from app.config import settings

# Set up logger
logger = logging.getLogger(__name__)


class TextExtractionResult(NamedTuple):
    """Result of text extraction attempt"""

    success: bool
    text: str
    error_message: str | None = None
    extraction_method: str | None = None  # 'pdftotext', 'textract', etc.


class FallbackResult(NamedTuple):
    """Result of file processing with potential fallback"""

    text: str | None
    filename: str
    mode_used: str  # 'text' or 'multimodal'
    fallback_triggered: bool
    fallback_reason: str | None = None
    file_base64: str | None = None  # Only populated if fallback to multimodal


# Content validation functions
def validate_content_quality(text: str) -> tuple[bool, str | None]:
    """
    Validate if extracted text content appears to be of good quality.

    Args:
        text: Extracted text content

    Returns:
        Tuple of (is_valid, reason_if_invalid)
    """
    if not text:
        return False, "Empty content"

    text = text.strip()
    if not text:
        return False, "Content contains only whitespace"

    # Check minimum length threshold
    if len(text) < settings.MIN_CONTENT_LENGTH_THRESHOLD:
        return (
            False,
            f"Content too short ({len(text)} chars, minimum {settings.MIN_CONTENT_LENGTH_THRESHOLD})",
        )

    if not settings.CONTENT_QUALITY_CHECK:
        # Skip quality checks if disabled
        return True, None

    # Check for excessive symbols/special characters (garbled content)
    if settings.MAX_SYMBOL_RATIO > 0:
        symbol_count = len(re.findall(r'[^\w\s\-.,;:!?()"\']', text))
        symbol_ratio = symbol_count / len(text) if len(text) > 0 else 0

        if symbol_ratio > settings.MAX_SYMBOL_RATIO:
            return (
                False,
                f"Content appears garbled (symbol ratio: {symbol_ratio:.2f}, max: {settings.MAX_SYMBOL_RATIO})",
            )

    # Check for meaningful content (at least some alphabetic characters)
    alphabetic_chars = len(re.findall(r"[a-zA-Z]", text))
    if alphabetic_chars < 10:
        return (
            False,
            f"Content lacks meaningful text (only {alphabetic_chars} alphabetic characters)",
        )

    # Check for reasonable word-to-character ratio
    words = re.findall(r"\b\w+\b", text)
    if len(words) < 5:
        return False, f"Content has too few words ({len(words)} words)"

    return True, None


def should_fallback_to_multimodal(
    extraction_result: TextExtractionResult, multimodal_enabled: bool = True
) -> tuple[bool, str | None]:
    """
    Determine if we should fallback to multimodal parsing.

    Args:
        extraction_result: Result of text extraction attempt
        multimodal_enabled: Whether multimodal parsing is available

    Returns:
        Tuple of (should_fallback, reason)
    """
    if not settings.ENABLE_TEXTRACT_FALLBACK or not multimodal_enabled:
        return False, "Fallback disabled"

    # If extraction completely failed
    if not extraction_result.success:
        return True, f"Text extraction failed: {extraction_result.error_message}"

    # If extraction succeeded but content quality is poor
    is_valid, quality_reason = validate_content_quality(extraction_result.text)
    if not is_valid:
        return True, f"Poor content quality: {quality_reason}"

    return False, None


class FileProcessor:
    """Base class for file processing"""

    @staticmethod
    def validate_file(file: UploadFile, allowed_extensions: set, max_size: int) -> None:
        """Validate uploaded file"""
        # Check file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"File type {file_ext} not supported. Allowed types: {', '.join(allowed_extensions)}",
            )

        # Check file size
        file.file.seek(0, 2)  # Move to end of file
        file_size = file.file.tell()
        file.file.seek(0)  # Reset to beginning

        if file_size > max_size:
            raise HTTPException(
                status_code=400,
                detail=f"File size exceeds maximum allowed size of {max_size / (1024 * 1024):.1f}MB",
            )

    @staticmethod
    async def save_temp_file(file: UploadFile) -> str:
        """Save uploaded file to temporary location"""
        try:
            with tempfile.NamedTemporaryFile(
                delete=False, suffix=Path(file.filename).suffix
            ) as tmp_file:
                content = await file.read()
                tmp_file.write(content)
                return tmp_file.name
        except Exception as e:
            logger.error(f"Error saving temporary file {file.filename}: {str(e)}")
            raise HTTPException(
                status_code=500, detail=f"Failed to save uploaded file: {str(e)}"
            )


class TextractProcessor(FileProcessor):
    """Unified processor for all document types using direct commands and textract"""

    @staticmethod
    def handle_pdftotext(filename: str) -> str:
        """Handle PDF text extraction using pdftotext command with layout preservation"""
        try:
            args = ["pdftotext", "-layout", filename, "-"]
            result = subprocess.run(
                args,
                capture_output=True,
                text=True,
                timeout=30,  # 30 second timeout
                check=True,
            )
            return result.stdout
        except subprocess.TimeoutExpired:
            logger.error(f"pdftotext command timed out for file: {filename}")
            return ""
        except subprocess.CalledProcessError as e:
            logger.error(f"pdftotext command failed for file {filename}: {e}")
            return ""
        except FileNotFoundError:
            logger.error("pdftotext command not found. Please install poppler-utils.")
            raise HTTPException(
                status_code=500,
                detail="PDF processing requires pdftotext. Please install poppler-utils: brew install poppler (macOS) or apt-get install poppler-utils (Linux)",
            )
        except Exception as e:
            logger.error(f"Unexpected error in pdftotext for file {filename}: {str(e)}")
            return ""

    @staticmethod
    async def extract_text_async(file_path: str, file_ext: str) -> str:
        """Async wrapper for text extraction to avoid blocking I/O"""
        # Ensure file extension is lowercase for consistent comparison
        file_ext = file_ext.lower()
        logger.debug(f"Extracting text from {file_ext} file: {file_path}")

        if file_ext == ".pdf":
            # Use direct pdftotext command in thread pool to avoid blocking
            logger.debug("Using pdftotext command for PDF extraction")
            text = await asyncio.to_thread(
                TextractProcessor.handle_pdftotext, file_path
            )
        else:
            # Use textract for DOC and DOCX files in thread pool
            logger.debug(f"Using textract for {file_ext} file extraction")
            text = await asyncio.to_thread(
                lambda: textract.process(file_path, encoding="utf-8").decode("utf-8")
            )

        if not text or not text.strip():
            raise ValueError(f"No text content found in {file_ext.upper()} file")

        logger.debug(f"Successfully extracted text: {len(text)} characters")
        return text

    @staticmethod
    def extract_text(file_path: str, file_ext: str) -> str:
        """Synchronous extract text method for backward compatibility"""
        try:
            # Ensure file extension is lowercase for consistent comparison
            file_ext = file_ext.lower()
            logger.debug(f"Using sync extraction for {file_ext} file")

            # Configure extraction based on file type
            if file_ext == ".pdf":
                # Use direct pdftotext command with layout preservation
                text = TextractProcessor.handle_pdftotext(file_path)
            else:
                # For DOC and DOCX files
                text = textract.process(file_path, encoding="utf-8").decode("utf-8")

            if not text.strip():
                raise ValueError(f"No text content found in {file_ext.upper()} file")

            logger.debug(f"Successfully extracted text: {len(text)} characters")
            return text

        except ValueError:
            # Re-raise ValueError as-is
            raise
        except HTTPException:
            # Re-raise HTTPException as-is
            raise
        except UnicodeDecodeError as e:
            # If UTF-8 decoding fails, try with error handling
            logger.warning(
                f"UTF-8 decoding failed, trying with error handling: {str(e)}"
            )
            try:
                if file_ext == ".pdf":
                    # For PDF, pdftotext should handle encoding properly
                    raise ValueError(
                        "PDF text extraction failed due to encoding issues"
                    )
                else:
                    text = textract.process(file_path).decode("utf-8", errors="ignore")

                if text.strip():
                    logger.debug(
                        f"Successfully extracted text with error handling: {len(text)} characters"
                    )
                    return text
            except Exception as e2:
                logger.error(
                    f"Text extraction with error handling also failed: {str(e2)}"
                )

        except Exception as e:
            logger.error(f"Text extraction failed for {file_ext} file: {str(e)}")

            # Provide detailed error messages
            error_msg = str(e).lower()
            if "unsupported" in error_msg or "format" in error_msg:
                raise HTTPException(
                    status_code=400,
                    detail=f"This {file_ext.upper()} file format is not supported. Please check the file format.",
                )
            elif "corrupted" in error_msg or "invalid" in error_msg:
                raise HTTPException(
                    status_code=400,
                    detail=f"The {file_ext.upper()} file appears to be corrupted or invalid.",
                )
            elif "password" in error_msg or "encrypted" in error_msg:
                raise HTTPException(
                    status_code=400,
                    detail=f"Password-protected {file_ext.upper()} files are not supported.",
                )
            else:
                # Generic error
                raise HTTPException(
                    status_code=400,
                    detail=f"Error processing {file_ext.upper()} file: {str(e)}",
                )

    @staticmethod
    async def try_extract_text_async(
        file_path: str, file_ext: str
    ) -> TextExtractionResult:
        """
        Attempt text extraction and return structured result instead of throwing exceptions.

        Args:
            file_path: Path to the file
            file_ext: File extension

        Returns:
            TextExtractionResult with success status and extracted text or error message
        """
        try:
            file_ext = file_ext.lower()
            logger.debug(
                f"Attempting text extraction from {file_ext} file: {file_path}"
            )

            if file_ext == ".pdf":
                # Use direct pdftotext command in thread pool
                text = await asyncio.to_thread(
                    TextractProcessor.handle_pdftotext, file_path
                )
                extraction_method = "pdftotext"
            else:
                # Use textract for DOC and DOCX files in thread pool
                text = await asyncio.to_thread(
                    lambda: textract.process(file_path, encoding="utf-8").decode(
                        "utf-8"
                    )
                )
                extraction_method = "textract"

            if not text or not text.strip():
                return TextExtractionResult(
                    success=False,
                    text="",
                    error_message=f"No text content found in {file_ext.upper()} file",
                    extraction_method=extraction_method,
                )

            logger.debug(
                f"Successfully extracted text: {len(text)} characters using {extraction_method}"
            )
            return TextExtractionResult(
                success=True,
                text=text,
                error_message=None,
                extraction_method=extraction_method,
            )

        except ValueError as e:
            # Handle specific extraction failures
            return TextExtractionResult(
                success=False,
                text="",
                error_message=str(e),
                extraction_method=file_ext.replace(".", ""),
            )
        except HTTPException as e:
            # Handle HTTP exceptions (missing dependencies, etc.)
            return TextExtractionResult(
                success=False,
                text="",
                error_message=e.detail,
                extraction_method=file_ext.replace(".", ""),
            )
        except UnicodeDecodeError as e:
            # Handle encoding issues
            logger.warning(f"UTF-8 decoding failed: {str(e)}")
            try:
                if file_ext == ".pdf":
                    # For PDF, pdftotext should handle encoding properly
                    error_msg = "PDF text extraction failed due to encoding issues"
                else:
                    # Try with error handling for other formats
                    text = await asyncio.to_thread(
                        lambda: textract.process(file_path).decode(
                            "utf-8", errors="ignore"
                        )
                    )
                    if text.strip():
                        logger.debug(
                            f"Successfully extracted text with error handling: {len(text)} characters"
                        )
                        return TextExtractionResult(
                            success=True,
                            text=text,
                            error_message=None,
                            extraction_method="textract_fallback",
                        )
                    error_msg = "Text extraction with encoding fallback also failed"

                return TextExtractionResult(
                    success=False,
                    text="",
                    error_message=error_msg,
                    extraction_method=file_ext.replace(".", ""),
                )
            except Exception as e2:
                return TextExtractionResult(
                    success=False,
                    text="",
                    error_message=f"Text extraction with error handling failed: {str(e2)}",
                    extraction_method=file_ext.replace(".", ""),
                )
        except Exception as e:
            # Handle any other exceptions
            logger.error(f"Unexpected error during text extraction: {str(e)}")
            return TextExtractionResult(
                success=False,
                text="",
                error_message=f"Unexpected error during text extraction: {str(e)}",
                extraction_method=file_ext.replace(".", ""),
            )


class PDFToImageConverter:
    """Converter for PDF files to images for multimodal processing"""

    @staticmethod
    def _convert_pdf_bytes_to_images(pdf_bytes: bytes, filename: str) -> list:
        """
        Blocking function to convert PDF bytes to PIL Images.
        This is called within asyncio.to_thread() to avoid blocking the event loop.
        """
        logger.debug(
            f"PDF size: {len(pdf_bytes)} bytes, DPI: {settings.PDF_TO_IMAGE_DPI}, Max pages: {settings.PDF_MAX_PAGES}"
        )

        # Convert PDF bytes to PIL Images
        images = convert_from_bytes(
            pdf_bytes,
            dpi=settings.PDF_TO_IMAGE_DPI,
            first_page=1,
            last_page=settings.PDF_MAX_PAGES,  # Limit pages to prevent excessive API costs
            fmt=settings.PDF_IMAGE_FORMAT.lower(),
        )

        if not images:
            raise ValueError("No pages found in PDF")

        logger.info(f"Successfully converted PDF to {len(images)} images: {filename}")
        return images

    @staticmethod
    def _convert_image_to_base64(image, image_format: str, page_num: int) -> str:
        """
        Blocking function to convert a PIL Image to base64.
        This is called within asyncio.to_thread() to avoid blocking the event loop.
        """
        # Convert PIL Image to base64
        img_buffer = io.BytesIO()

        # Save as PNG for better quality or JPEG for smaller size
        if image_format not in ["PNG", "JPEG"]:
            image_format = "PNG"  # Default fallback

        image.save(
            img_buffer,
            format=image_format,
            quality=90 if image_format == "JPEG" else None,
        )
        img_buffer.seek(0)

        # Encode to base64
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode("utf-8")

        logger.debug(
            f"Converted page {page_num} to base64, size: {len(img_base64)} chars"
        )
        return img_base64

    @staticmethod
    async def convert_pdf_to_images(
        pdf_bytes: bytes, filename: str = "document.pdf"
    ) -> list[str]:
        """
        Convert PDF bytes to list of base64-encoded images

        Args:
            pdf_bytes: PDF file content as bytes
            filename: Original filename for logging

        Returns:
            List of base64-encoded image strings (PNG format)

        Raises:
            HTTPException: If PDF conversion fails
        """
        try:
            logger.info(f"Converting PDF to images: {filename}")

            # Convert PDF bytes to PIL Images (blocking operation in thread pool)
            images = await asyncio.to_thread(
                PDFToImageConverter._convert_pdf_bytes_to_images, pdf_bytes, filename
            )

            # Convert images to base64 strings
            base64_images = []
            image_format = settings.PDF_IMAGE_FORMAT.upper()

            # Process images in parallel using asyncio.gather for better performance
            tasks = []
            for i, image in enumerate(images):
                task = asyncio.to_thread(
                    PDFToImageConverter._convert_image_to_base64,
                    image,
                    image_format,
                    i + 1,
                )
                tasks.append(task)

            try:
                base64_images = await asyncio.gather(*tasks)
            except Exception as e:
                logger.error(f"Error converting images to base64: {str(e)}")
                raise ValueError(f"Failed to convert images to base64: {str(e)}")

            logger.info(
                f"Successfully converted {len(base64_images)} pages to base64 images: {filename}"
            )
            return base64_images

        except Exception as e:
            logger.error(
                f"Error converting PDF to images for {filename}: {str(e)}",
                exc_info=True,
            )

            # Provide specific error messages for common issues
            error_msg = str(e)
            if "poppler" in error_msg.lower():
                raise HTTPException(
                    status_code=500,
                    detail="PDF conversion requires poppler-utils. Please install it using: brew install poppler (macOS) or apt-get install poppler-utils (Linux)",
                )
            elif "corrupted" in error_msg.lower() or "invalid" in error_msg.lower():
                raise HTTPException(
                    status_code=400, detail=f"Invalid or corrupted PDF file: {filename}"
                )
            elif "password" in error_msg.lower() or "encrypted" in error_msg.lower():
                raise HTTPException(
                    status_code=400,
                    detail=f"Password-protected PDF files are not supported: {filename}",
                )
            else:
                raise HTTPException(
                    status_code=500,
                    detail=f"Error converting PDF to images: {error_msg}",
                )


class FileProcessorFactory:
    """Factory to get appropriate processor based on file type"""

    supported_extensions = {".pdf", ".doc", ".docx"}

    @classmethod
    def get_processor(cls, file_extension: str) -> FileProcessor:
        """Get processor for given file extension"""
        if file_extension.lower() not in cls.supported_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"No processor available for file type: {file_extension}",
            )
        return TextractProcessor()


async def process_file(
    file: UploadFile, allowed_extensions: set, max_size: int
) -> tuple[str, str]:
    """
    Process uploaded file and extract text
    Returns: (extracted_text, original_filename)
    """
    try:
        # Validate file
        FileProcessor.validate_file(file, allowed_extensions, max_size)

        # Save to temporary file
        temp_path = await FileProcessor.save_temp_file(file)

        try:
            # Get appropriate processor
            file_ext = Path(file.filename).suffix.lower()
            processor = FileProcessorFactory.get_processor(file_ext)

            # Extract text using async method to avoid blocking I/O
            text = await processor.extract_text_async(temp_path, file_ext)

            if not text or not text.strip():
                raise HTTPException(
                    status_code=400,
                    detail=f"No text content could be extracted from {file.filename}. Please check if the file contains readable text.",
                )

            return text, file.filename
        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    except HTTPException:
        # Re-raise HTTPExceptions without modification
        raise
    except UnicodeDecodeError as e:
        logger.error(f"UTF-8 decode error processing {file.filename}: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=f"Error processing {file.filename}: File contains invalid character encoding. Please ensure the file is not corrupted.",
        )
    except Exception as e:
        logger.error(f"Unexpected error processing {file.filename}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error processing {file.filename}: {str(e)}",
        )


async def get_file_base64(file: UploadFile) -> tuple[str, str]:
    """
    Convert file to base64 for multimodal processing
    Returns: (base64_content, mime_type)
    """
    content = await file.read()
    await file.seek(0)  # Reset file pointer

    # Determine MIME type
    file_ext = Path(file.filename).suffix.lower()
    mime_types = {
        ".pdf": "application/pdf",
        ".doc": "application/msword",
        ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    }

    mime_type = mime_types.get(file_ext, "application/octet-stream")
    base64_content = base64.b64encode(content).decode("utf-8")

    return base64_content, mime_type


async def process_file_with_fallback(
    file: UploadFile,
    allowed_extensions: set,
    max_size: int,
    force_multimodal: bool = False,
) -> FallbackResult:
    """
    Process uploaded file with automatic fallback to multimodal if text extraction fails.

    Args:
        file: Uploaded file
        allowed_extensions: Set of allowed file extensions
        max_size: Maximum file size in bytes
        force_multimodal: If True, skip text extraction and go directly to multimodal

    Returns:
        FallbackResult containing the processing results and mode used

    Raises:
        HTTPException: Only for validation errors (file type, size, etc.)
    """
    try:
        # Validate file first
        FileProcessor.validate_file(file, allowed_extensions, max_size)
        filename = file.filename

        # Check if multimodal is enabled
        multimodal_enabled = settings.ENABLE_MULTIMODAL

        # If forced to multimodal or multimodal not enabled, handle accordingly
        if force_multimodal:
            if not multimodal_enabled:
                raise HTTPException(
                    status_code=400,
                    detail="Multimodal parsing is disabled but was requested",
                )
            logger.info(f"Forced multimodal mode for file: {filename}")
            file_base64, _ = await get_file_base64(file)
            return FallbackResult(
                text=None,
                filename=filename,
                mode_used="multimodal",
                fallback_triggered=False,
                fallback_reason="Forced multimodal mode",
                file_base64=file_base64,
            )

        # Try text extraction first
        logger.info(f"Attempting text extraction for file: {filename}")
        temp_path = await FileProcessor.save_temp_file(file)

        try:
            file_ext = Path(filename).suffix.lower()
            processor = FileProcessorFactory.get_processor(file_ext)

            # Use the new non-throwing extraction method
            extraction_result = await processor.try_extract_text_async(
                temp_path, file_ext
            )

            # Check if we should fallback to multimodal
            should_fallback, fallback_reason = should_fallback_to_multimodal(
                extraction_result, multimodal_enabled
            )

            if not should_fallback:
                # Text extraction was successful and content is good quality
                logger.info(
                    f"Text extraction successful for {filename}: {len(extraction_result.text)} characters"
                )
                return FallbackResult(
                    text=extraction_result.text,
                    filename=filename,
                    mode_used="text",
                    fallback_triggered=False,
                    fallback_reason=None,
                    file_base64=None,
                )

            # Need to fallback to multimodal
            if not multimodal_enabled:
                # Fallback needed but multimodal is disabled
                logger.error(
                    f"Text extraction failed for {filename} but multimodal fallback is disabled: {fallback_reason}"
                )
                raise HTTPException(
                    status_code=400,
                    detail=f"Text extraction failed and multimodal fallback is disabled. Error: {fallback_reason}",
                )

            logger.warning(
                f"Falling back to multimodal for {filename}: {fallback_reason}"
            )

            # Reset file pointer for base64 conversion
            await file.seek(0)
            file_base64, _ = await get_file_base64(file)

            return FallbackResult(
                text=None,
                filename=filename,
                mode_used="multimodal",
                fallback_triggered=True,
                fallback_reason=fallback_reason,
                file_base64=file_base64,
            )

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    except HTTPException:
        # Re-raise HTTPExceptions (validation errors) without modification
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing {file.filename}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error processing {file.filename}: {str(e)}",
        )
