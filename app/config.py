import os

from dotenv import load_dotenv

load_dotenv()


class Settings:
    # OpenAI settings
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4o-mini")

    # API settings
    API_TITLE: str = "CV Parser API"
    API_VERSION: str = "1.2.1"
    API_DESCRIPTION: str = "API for parsing CV/Resume files using LangChain and OpenAI"

    # File upload settings
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: set = {".pdf", ".doc", ".docx"}

    # Parser settings
    PARSER_VERSION: str = "1.2.1"
    TEMPERATURE: float = 0.1  # Low temperature for consistent parsing
    MAX_RETRIES: int = 3

    # Multimodal settings
    ENABLE_MULTIMODAL: bool = True
    PDF_TO_IMAGE_DPI: int = int(
        os.getenv("PDF_TO_IMAGE_DPI", "200")
    )  # DPI for PDF to image conversion
    PDF_MAX_PAGES: int = int(
        os.getenv("PDF_MAX_PAGES", "10")
    )  # Max pages to process in multimodal mode
    PDF_IMAGE_FORMAT: str = os.getenv("PDF_IMAGE_FORMAT", "PNG")  # PNG or JPEG

    # Textract fallback settings
    ENABLE_TEXTRACT_FALLBACK: bool = (
        os.getenv("ENABLE_TEXTRACT_FALLBACK", "true").lower() == "true"
    )
    MIN_CONTENT_LENGTH_THRESHOLD: int = int(
        os.getenv("MIN_CONTENT_LENGTH_THRESHOLD", "150")
    )  # Minimum characters for valid content
    CONTENT_QUALITY_CHECK: bool = (
        os.getenv("CONTENT_QUALITY_CHECK", "true").lower() == "true"
    )
    MAX_SYMBOL_RATIO: float = float(
        os.getenv("MAX_SYMBOL_RATIO", "0.3")
    )  # Maximum ratio of symbols to total characters before considering content garbled

    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # Redis cache settings
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "")
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))

    # Cache configuration
    CACHE_ENABLED: bool = os.getenv("CACHE_ENABLED", "true").lower() == "true"
    CACHE_TTL_SUCCESS: int = int(os.getenv("CACHE_TTL_SUCCESS", "604800"))  # 7 days
    CACHE_KEY_PREFIX: str = os.getenv("CACHE_KEY_PREFIX", "cv_parser")
    CACHE_TIMEOUT: int = int(
        os.getenv("CACHE_TIMEOUT", "5")
    )  # Redis operation timeout in seconds


settings = Settings()
