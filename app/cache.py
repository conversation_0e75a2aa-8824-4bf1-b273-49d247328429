import asyncio
import hashlib
import json
import logging
from typing import Any, Optional

import redis.asyncio as redis
from pydantic import ValidationError

from app.config import settings
from app.models import CVData
from app.parser import CVParserState

logger = logging.getLogger(__name__)


class CacheService:
    """Redis cache service for CV parser workflow results"""

    def __init__(self):
        self._redis_client: Optional[redis.Redis] = None
        self._connection_pool: Optional[redis.ConnectionPool] = None
        self._is_connected = False

    async def connect(self) -> bool:
        """Initialize Redis connection with error handling"""
        if not settings.CACHE_ENABLED:
            logger.info("Cache is disabled in configuration")
            return False

        try:
            # Create connection pool for better performance
            if settings.REDIS_URL:
                # Use Redis URL if provided
                self._connection_pool = redis.ConnectionPool.from_url(
                    settings.REDIS_URL,
                    max_connections=20,
                    retry_on_timeout=True,
                    socket_timeout=settings.CACHE_TIMEOUT,
                    socket_connect_timeout=settings.CACHE_TIMEOUT,
                )
            else:
                # Use individual Redis settings
                self._connection_pool = redis.ConnectionPool(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    password=settings.REDIS_PASSWORD
                    if settings.REDIS_PASSWORD
                    else None,
                    db=settings.REDIS_DB,
                    max_connections=20,
                    retry_on_timeout=True,
                    socket_timeout=settings.CACHE_TIMEOUT,
                    socket_connect_timeout=settings.CACHE_TIMEOUT,
                )

            self._redis_client = redis.Redis(connection_pool=self._connection_pool)

            # Test the connection
            await asyncio.wait_for(
                self._redis_client.ping(), timeout=settings.CACHE_TIMEOUT
            )

            self._is_connected = True
            logger.info("Successfully connected to Redis cache")
            return True

        except asyncio.TimeoutError:
            logger.warning("Redis connection timeout - cache will be disabled")
            self._is_connected = False
            return False
        except Exception as e:
            logger.warning(
                f"Failed to connect to Redis: {str(e)} - cache will be disabled"
            )
            self._is_connected = False
            return False

    async def disconnect(self):
        """Close Redis connection"""
        if self._redis_client:
            try:
                await self._redis_client.close()
                logger.info("Redis connection closed")
            except Exception as e:
                logger.warning(f"Error closing Redis connection: {str(e)}")
        self._is_connected = False

    def is_available(self) -> bool:
        """Check if cache is available"""
        return (
            settings.CACHE_ENABLED
            and self._is_connected
            and self._redis_client is not None
        )

    def generate_cache_key(self, initial_state: CVParserState) -> str:
        """Generate cache key based on input content and parsing parameters"""
        try:
            # Determine the content to hash based on mode
            if initial_state["mode"] == "text":
                content = initial_state.get("input_text", "")
            elif initial_state["mode"] == "multimodal":
                content = initial_state.get("input_file_base64", "")
            else:
                content = ""

            if not content:
                raise ValueError("No content available for cache key generation")

            # Create content hash
            content_hash = hashlib.sha256(content.encode("utf-8")).hexdigest()[:16]

            # Include model version to invalidate cache on model changes
            model_version = settings.OPENAI_MODEL.replace(".", "_").replace("-", "_")

            # Format: cv_parser:{mode}:{content_hash}:{model_version}
            cache_key = f"{settings.CACHE_KEY_PREFIX}:{initial_state['mode']}:{content_hash}:{model_version}"

            logger.debug(f"Generated cache key: {cache_key}")
            return cache_key

        except Exception as e:
            logger.error(f"Error generating cache key: {str(e)}")
            # Return a fallback key that won't match anything
            return f"{settings.CACHE_KEY_PREFIX}:error:no_key"

    async def get_cached_result(self, cache_key: str) -> Optional[dict[str, Any]]:
        """Retrieve cached workflow result"""
        if not self.is_available():
            return None

        try:
            # Get from Redis with timeout
            cached_data = await asyncio.wait_for(
                self._redis_client.get(cache_key), timeout=settings.CACHE_TIMEOUT
            )

            if cached_data is None:
                logger.debug(f"Cache miss for key: {cache_key}")
                return None

            # Deserialize JSON data
            result_data = json.loads(cached_data)
            logger.info(f"Cache hit for key: {cache_key}")

            return result_data

        except asyncio.TimeoutError:
            logger.warning(f"Cache get timeout for key: {cache_key}")
            return None
        except json.JSONDecodeError as e:
            logger.error(
                f"Failed to deserialize cached data for key {cache_key}: {str(e)}"
            )
            # Remove corrupted cache entry
            await self._delete_cache_entry(cache_key)
            return None
        except Exception as e:
            logger.error(f"Error retrieving from cache for key {cache_key}: {str(e)}")
            return None

    async def cache_result(
        self, cache_key: str, result: dict[str, Any], is_error: bool = False
    ) -> bool:
        """Cache workflow result (only successful results)"""
        if not self.is_available():
            return False

        try:
            # Use success TTL for all cached results
            ttl = settings.CACHE_TTL_SUCCESS

            # Serialize result to JSON
            serialized_data = json.dumps(result, default=self._json_serializer)

            # Store in Redis with timeout
            await asyncio.wait_for(
                self._redis_client.setex(cache_key, ttl, serialized_data),
                timeout=settings.CACHE_TIMEOUT,
            )

            logger.info(f"Cached result for key: {cache_key} (TTL: {ttl}s)")
            return True

        except asyncio.TimeoutError:
            logger.warning(f"Cache set timeout for key: {cache_key}")
            return False
        except Exception as e:
            logger.error(f"Error caching result for key {cache_key}: {str(e)}")
            return False

    async def _delete_cache_entry(self, cache_key: str):
        """Delete a specific cache entry (for corrupted data)"""
        try:
            if self.is_available():
                await asyncio.wait_for(
                    self._redis_client.delete(cache_key), timeout=settings.CACHE_TIMEOUT
                )
                logger.info(f"Deleted corrupted cache entry: {cache_key}")
        except Exception as e:
            logger.error(f"Error deleting cache entry {cache_key}: {str(e)}")

    def _json_serializer(self, obj: Any) -> Any:
        """Custom JSON serializer for Pydantic models"""
        if hasattr(obj, "model_dump"):
            return obj.model_dump()
        elif hasattr(obj, "dict"):
            return obj.dict()
        else:
            return str(obj)

    def deserialize_cvdata(self, data: dict) -> Optional[CVData]:
        """Deserialize cached data back to CVData model"""
        try:
            if "parsed_data" in data and data["parsed_data"]:
                return CVData(**data["parsed_data"])
            return None
        except ValidationError as e:
            logger.error(f"Failed to deserialize CVData: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error deserializing CVData: {str(e)}")
            return None

    async def clear_cache_by_pattern(self, pattern: str = None) -> int:
        """Clear cache entries matching a pattern (admin function)"""
        if not self.is_available():
            return 0

        try:
            if pattern is None:
                pattern = f"{settings.CACHE_KEY_PREFIX}:*"

            keys = await asyncio.wait_for(
                self._redis_client.keys(pattern), timeout=settings.CACHE_TIMEOUT
            )

            if keys:
                deleted_count = await asyncio.wait_for(
                    self._redis_client.delete(*keys), timeout=settings.CACHE_TIMEOUT
                )
                logger.info(
                    f"Cleared {deleted_count} cache entries matching pattern: {pattern}"
                )
                return deleted_count
            else:
                logger.info(f"No cache entries found matching pattern: {pattern}")
                return 0

        except Exception as e:
            logger.error(f"Error clearing cache with pattern {pattern}: {str(e)}")
            return 0


# Global cache service instance
cache_service = CacheService()


async def initialize_cache():
    """Initialize the global cache service"""
    await cache_service.connect()


async def shutdown_cache():
    """Shutdown the global cache service"""
    await cache_service.disconnect()
