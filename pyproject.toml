[project]
name = "cv-parser-api"
version = "1.2.1"
description = "CV Parser API using LangChain and OpenAI"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "pydantic>=2.5.0",
    "python-dotenv>=1.0.0",
    "langchain>=0.1.0",
    "langchain-openai>=0.0.5",
    "langgraph>=0.0.20",
    "pdf2image>=1.16.0",
    "Pillow>=10.0.0",
    "textract>=1.6.0",
    "typing-extensions>=4.8.0",
    "aiofiles>=23.2.1",
    "phonenumbers>=9.0.7",
    "pdftotext>=3.0.0",
    "redis>=5.0.0",
    "langchain-community>=0.3.25",
    "tenacity>=8.0.0",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.1.0",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "httpx>=0.28.1",
    "langfuse>=3.0.2",
    "locust>=2.37.10",
    "pre-commit>=4.2.0",
    "pytest>=8.4.0",
    "pytest-asyncio>=1.0.0",
    "ruff>=0.11.13",
]

[tool.hatch.build.targets.wheel]
packages = ["app"]
