# Changelog

All notable changes to the CV Parser API project will be documented in this file.

## [1.3.4] - 2025-06-20

### 🔧 Testing Infrastructure & Data Schema Enhancements

#### Test Suite Improvements
- **FIXED**: Updated all tests for new modular parser structure after refactoring
- **ENHANCED**: Test patches updated to reflect new section parser imports and organization
- **COMPATIBILITY**: All existing test functionality preserved with new architecture
- **RELIABILITY**: Maintained 100% test pass rate through parser refactoring

#### Data Schema Evolution
- **IMPROVED**: Enhanced data schema with more structured types for better data consistency
- **VALIDATION**: Strengthened type validation across all CV data models
- **CONSISTENCY**: Unified data representations throughout the parsing pipeline

#### Parser Structure Optimization
- **REFINED**: Summary parsing moved to dedicated additional parser for better logical separation
- **UPDATED**: Parser prompts optimized for improved section-specific extraction
- **STREAMLINED**: Cleaner separation of concerns between different parsing modules

### Benefits
- **Testing Reliability**: Robust test coverage maintained through major architectural changes
- **Data Quality**: Enhanced schema validation ensures more consistent parsing results
- **Code Organization**: Improved logical separation of parsing responsibilities
- **Maintainability**: Easier to maintain and extend individual parser components

---

## [1.3.3] - 2025-06-19

### 🛠️ Codebase Refactoring & Architecture Improvements

#### Major Refactoring
- **REFACTORED**: Complete restructuring of parser codebase into modular architecture
- **IMPROVED**: Clear separation of concerns with dedicated modules for different responsibilities
- **NEW STRUCTURE**:
  - `parser/base.py`: Base parser classes and interfaces for extensibility
  - `parser/cv_parser.py`: Main CV parser orchestration logic
  - `parser/models.py`: Dedicated data models for parser state management
  - `parser/sections/`: Specialized parsers for different CV sections
  - `parser/validation/`: Content validation logic extracted into dedicated module
  - `parser/workflow/`: LangGraph workflow implementation separated from business logic
- **MAINTAINABILITY**: Reduced main parser.py from 1700+ lines to focused module structure
- **EXTENSIBILITY**: Easier to add new section parsers or validation rules

#### Import Updates
- **FIXED**: All imports updated to use new module structure
- **TESTS**: Test patches updated to reflect refactored parser paths
- **COMPATIBILITY**: Backward compatibility maintained for all public APIs

### Benefits
- **Developer Experience**: Much cleaner codebase structure for easier navigation
- **Maintainability**: Logical grouping of related functionality
- **Testing**: Easier to test individual components in isolation
- **Extensibility**: Simple to add new parsers or validation rules
- **Performance**: No runtime impact, purely organizational improvement

---

## [1.3.2] - 2025-06-19

### 🔁 Intelligent Retry Logic for Callback System

#### Retry Mechanism
- **NEW**: Tenacity-based retry logic for callback system
- **INTELLIGENT**: Automatic retry on recoverable errors:
  - Server errors (5xx status codes)
  - Timeout errors (408)
  - Rate limit errors (429)
  - Connection errors
- **NO RETRY**: Permanent client errors (400, 401, 403, 404) fail immediately
- **CONFIGURATION**: Exponential backoff with jitter (1-60s max, 3 attempts)

#### Technical Implementation
- **DEPENDENCY**: Added `tenacity` library for robust retry functionality
- **FUNCTION**: Enhanced `send_callback` with retry decorator
- **LOGGING**: Comprehensive retry attempt logging for monitoring
- **ERROR HANDLING**: Detailed error categorization for retry decisions

#### Testing
- **COMPREHENSIVE**: 11 new tests covering all retry scenarios
- **SCENARIOS**: Server errors, timeouts, rate limits, connection failures
- **VALIDATION**: Permanent errors correctly bypass retry logic
- **RELIABILITY**: Backward compatibility maintained

### Benefits
- **RELIABILITY**: Automatic recovery from transient network issues
- **RESILIENCE**: Better handling of temporary service unavailability
- **MONITORING**: Clear logging of retry attempts and reasons
- **USER EXPERIENCE**: Reduced failed callbacks due to temporary issues

---

## [1.3.1] - 2025-06-19

### 🛡️ Intelligent Textract Fallback System

#### Automatic Fallback Mechanism
- **NEW**: Intelligent fallback from text extraction to multimodal parsing when textract fails
- **SMART DETECTION**: Automatic detection of poor content quality and extraction failures
- **CONFIGURABLE**: Four new configuration settings for fine-tuned fallback control:
  - `ENABLE_TEXTRACT_FALLBACK`: Enable/disable automatic fallback (default: true)
  - `MIN_CONTENT_LENGTH_THRESHOLD`: Minimum characters for valid content (default: 150)
  - `CONTENT_QUALITY_CHECK`: Enable content quality validation (default: true)
  - `MAX_SYMBOL_RATIO`: Maximum symbol ratio before content is considered garbled (default: 0.3)

#### Content Quality Validation
- **ADVANCED VALIDATION**: Multi-layered content quality assessment:
  - Empty content detection
  - Minimum length validation (150 characters)
  - Garbled content detection via symbol ratio analysis
  - Meaningful text validation (alphabetic character count)
  - Word count validation for coherent content
- **FALLBACK TRIGGERS**: Automatic multimodal fallback when:
  - Text extraction completely fails
  - Extracted content is empty or too short
  - Content appears garbled (high symbol-to-text ratio)
  - Content lacks meaningful alphabetic characters

#### Enhanced File Processing
- **NEW ARCHITECTURE**: `process_file_with_fallback()` as main entry point
- **STRUCTURED RESULTS**: New data structures for better error tracking:
  - `TextExtractionResult`: Detailed extraction attempt results
  - `FallbackResult`: Complete processing outcome with fallback information
- **SEAMLESS INTEGRATION**: All API endpoints now use fallback mechanism automatically
- **LOGGING**: Comprehensive fallback scenario logging for monitoring and debugging

#### Workflow State Enhancement
- **TRACKING FIELDS**: Added fallback tracking to CVParserState:
  - `fallback_triggered`: Whether fallback to multimodal was used
  - `fallback_reason`: Detailed reason for fallback decision
  - `original_extraction_method`: Original text extraction method attempted
- **OBSERVABILITY**: Enhanced workflow logging with fallback information

#### Testing & Quality Assurance
- **COMPREHENSIVE TESTS**: 18 new tests in `test_fallback_mechanism.py` covering:
  - Content validation edge cases
  - Fallback decision logic
  - End-to-end fallback processing
  - Configuration-driven behavior
- **INTEGRATION**: All existing tests updated to support new fallback mechanism
- **RELIABILITY**: 262/262 tests passing with fallback functionality

#### Configuration Update
- **THRESHOLD ADJUSTMENT**: Updated `MIN_CONTENT_LENGTH_THRESHOLD` from 50 to 150 characters for better quality control

### Benefits
- **RELIABILITY**: No more failed parsing due to poor text extraction
- **INTELLIGENCE**: Automatic quality assessment and appropriate mode selection
- **TRANSPARENCY**: Full visibility into fallback decisions and reasons
- **BACKWARDS COMPATIBILITY**: Existing API contracts maintained
- **PERFORMANCE**: Minimal overhead when text extraction works well

---

## [1.3.0] - 2025-06-19

### 🚀 Parallel Multimodal Parsing Enhancement

#### Split Multimodal API Calls
- **NEW**: Parallel multimodal parsing with separate vision parsers for each CV section
- **ARCHITECTURE**: Three concurrent vision parsers instead of single monolithic call:
  - PersonalInfoEducationAndSkillsParser with vision support
  - WorkExperienceAndProjectsParser with vision support
  - AdditionalSectionsAndCertificationsParser with vision support
- **PERFORMANCE**: All three parsers run concurrently for optimal processing speed
- **ACCURACY**: Focused prompts for each section improve extraction quality

#### Technical Improvements
- **ADDED**: `parse_from_images()` method to all specialized parsers
- **OPTIMIZED**: Shared image processing - PDF conversion happens once
- **ENHANCED**: Multimodal prompts explicitly ignore irrelevant sections
- **REFACTORED**: `multimodal_api_call_node` renamed to `prepare_images_node`
- **STATE**: Added `prepared_images` and `image_mime_type` fields to workflow state

#### Benefits
- **Better Accuracy**: Each parser uses specialized prompts optimized for its sections
- **Improved Debugging**: Easier to identify and fix section-specific issues
- **Maintainability**: More modular code structure for future enhancements
- **Cost Efficiency**: Same number of API calls but more focused processing

---

## [1.2.3] - 2025-06-18

### 🔧 Developer Experience & Code Quality

#### Pre-commit Hooks Integration
- **NEW**: Pre-commit configuration with automated code quality checks
- **INTEGRATION**: Pytest integration for automated test execution
- **QUALITY**: Ruff linting and formatting in pre-commit pipeline
- **VALIDATION**: Type checking and validation in commit workflow
- **WORKFLOW**: Automated code quality enforcement for consistency

#### Enhanced Model Architecture
- **IMPROVED**: Pydantic model fields with detailed descriptions and comprehensive type annotations
- **DOCUMENTATION**: Enhanced field documentation for better API understanding
- **VALIDATION**: Stronger type validation with descriptive error messages
- **SCHEMA**: More robust schema definitions for data integrity

#### Documentation Improvements
- **UPDATED**: Comprehensive README updates with latest features and configurations
- **ADDED**: Redis caching documentation with configuration examples
- **ENHANCED**: Pre-commit hooks setup and usage instructions
- **IMPROVED**: Development workflow documentation with new tooling

---

## [1.2.2] - 2025-06-18

### 🚀 Performance & Processing Improvements

#### Redis Caching System
- **NEW**: Redis-based caching for CV parser workflow with resilient fallback mechanism
- **PERFORMANCE**: Significantly improved response times for repeated CV parsing requests
- **RELIABILITY**: Graceful fallback to normal processing when Redis is unavailable
- **SCALABILITY**: Reduced load on OpenAI API through intelligent caching

#### PDF Processing Enhancements
- **IMPROVED**: Replaced textract with direct pdftotext command for better PDF processing
- **UNIFIED**: Consolidated file processors using textract for all formats with PDF layout preservation
- **RELIABILITY**: More consistent text extraction across different PDF types
- **PERFORMANCE**: Faster PDF text extraction with improved accuracy

#### Parser Architecture Improvements
- **ENHANCED**: Combined skills extraction with personal info for better context
- **TRACING**: Improved request tracing and debugging capabilities
- **VALIDATION**: Enhanced content validation with improved thresholds and text processing
- **COMPATIBILITY**: V2 API now returns V1-compatible response structure for seamless integration

#### Code Quality & Maintenance
- **FORMATTING**: Applied comprehensive code formatting and dependency updates
- **TESTING**: Updated all test files to use correct API endpoint paths
- **STRUCTURE**: Streamlined API structure with updated endpoint naming conventions
- **VERSION**: Updated version to 1.2.2 across all configuration files

---

## [1.2.1] - 2025-06-17

### 🆔 Request Tracking & Performance Improvements

#### Request ID Feature
- **NEW**: UUID4-based request tracking for better observability and debugging
- **ADDED**: `request_id` field to all API responses and callback payloads
- **ENHANCED**: Request correlation between API calls and async callbacks
- **IMPROVED**: Logging with request ID context for better traceability
- **DEBUGGING**: Easy correlation of requests across sync and async processing

#### I/O Performance Enhancements
- **FIXED**: All blocking file operations replaced with async aiofiles
- **PERFORMANCE**: Non-blocking file read operations in `upload_file_to_dify()`
- **PERFORMANCE**: Non-blocking file write operations in temporary file handling
- **PERFORMANCE**: Non-blocking file deletion with proper error handling
- **SCALABILITY**: Improved concurrency under high-load conditions

#### API Response Updates
- **CVParseResponse**: Now includes `request_id` field for tracking
- **CallbackPayload**: Enhanced with `request_id` for request correlation
- **Logging**: Request ID prefixed logs for better debugging experience
- **Error Handling**: Request context preserved in error callbacks

#### Technical Improvements
- **ASYNC**: Full async file I/O pipeline for better performance
- **OBSERVABILITY**: Request lifecycle tracking from start to callback
- **PRODUCTION**: Enhanced error handling with request context
- **MAINTAINABILITY**: Cleaner async code patterns throughout

---

## [1.2.0] - 2025-06-17

### 🔄 API Endpoint Restructuring & Integration Improvements

#### Endpoint Path Updates
- **BREAKING**: Updated API endpoints to use `/internal` prefix for better service organization
  - `/parse` → `/internal/v2/parse` (OpenAI-based parsing)
  - `/v1/parse` → `/internal/v1/parse` (Callback-based parsing)
- **MAINTAINED**: All functionality preserved with cleaner endpoint structure
- **UPDATED**: Root endpoint now shows new endpoint paths in response

#### Simplified Callback Integration
- **SIMPLIFIED**: Removed complex streaming mode implementation for more reliable blocking operations
- **CLEANED**: Simplified callback payload structure by removing redundant workflow metadata fields:
  - Removed: `task_id`, `workflow_run_id`, `elapsed_time`, `total_tokens`, `total_steps`, `created_at`, `finished_at`
  - Kept: Essential fields for business logic (`status`, `success`, `profile`, `meta_data`, `original_filename`)
- **IMPROVED**: More reliable error handling and callback processing
- **REMOVED**: Unused functions and duplicate code to reduce complexity

#### Code Quality Improvements
- **REFACTORED**: Consolidated MockCallbackPayload with CallbackPayload to eliminate duplication
- **ENHANCED**: Updated mock callback endpoint to use shared payload model
- **CLEANED**: Removed 250+ lines of unused streaming and utility code
- **IMPROVED**: Better separation of concerns between OpenAI and callback integrations

#### Developer Experience
- **UPDATED**: Documentation with new endpoint paths and usage examples
- **IMPROVED**: Cleaner API structure for easier integration
- **ENHANCED**: Better error messages and logging for callback processing

---

## [1.1.3] - 2025-06-17

### 🖼️ PDF to Image Conversion for Multimodal Processing

#### Revolutionary Multimodal Enhancement
- **NEW**: Automatic PDF to image conversion for OpenAI Vision API compatibility
- **SOLVED**: Fixed "Invalid MIME type" error when processing PDFs in multimodal mode
- **FEATURE**: `PDFToImageConverter` class with comprehensive image processing capabilities
- **PERFORMANCE**: Multi-page PDFs processed in single OpenAI Vision API call

#### Advanced Configuration Options
- **CONFIGURABLE**: `PDF_TO_IMAGE_DPI` - Image quality control (default: 200 DPI)
- **COST CONTROL**: `PDF_MAX_PAGES` - Limit pages to prevent excessive API costs (default: 10)
- **FORMAT OPTIONS**: `PDF_IMAGE_FORMAT` - PNG for quality or JPEG for smaller sizes (default: PNG)
- **SMART FALLBACK**: Invalid format detection with automatic PNG fallback

#### Robust Error Handling
- **DEPENDENCY DETECTION**: Automatic poppler-utils availability checking
- **FILE VALIDATION**: Corrupted PDF and password-protected file detection
- **USER GUIDANCE**: Clear installation instructions for missing dependencies
- **GRACEFUL DEGRADATION**: Comprehensive error messages with actionable solutions

#### Enhanced Dependencies
- **ADDED**: `pdf2image` for PDF to image conversion
- **ADDED**: `Pillow` for image processing and optimization
- **REQUIRED**: `poppler-utils` system dependency for PDF processing

### 🧪 Comprehensive Testing
- **EXPANDED**: 7 new tests for PDFToImageConverter functionality
- **COVERAGE**: Error scenarios, format handling, and configuration testing
- **QUALITY**: All 186 tests passing with new PDF conversion features
- **RELIABILITY**: Mock-based testing for dependency-free test execution

### 📚 Documentation Updates
- **ENHANCED**: README.md with PDF conversion feature details
- **ADDED**: Configuration section for PDF to image settings
- **UPDATED**: Prerequisites to include poppler requirement
- **IMPROVED**: Architecture section with PDF processing workflow

### 🔧 Technical Improvements
- **INTEGRATION**: Seamless PDF conversion in `parse_from_file` method
- **OPTIMIZATION**: Single API call for multi-page PDF processing
- **LOGGING**: Detailed conversion process logging and debugging
- **COMPATIBILITY**: Maintains backward compatibility with existing API

---

## [1.1.2] - 2025-06-17

### 🔍 Comprehensive OpenAI API Error Logging

#### Enhanced Error Tracking & Debugging
- **NEW**: Comprehensive logging for all OpenAI API calls and errors
- **MONITORING**: Detailed error categorization (400/401/429/500 HTTP status codes)
- **DEBUGGING**: Full stack traces with contextual information (filename, operation type)
- **PERFORMANCE**: Debug-level logging for API call timing and data sizes

#### Smart Error Detection
- **400 Bad Request**: Automatic detection and logging of invalid base64 or rate limiting
- **401 Unauthorized**: API key validation error identification
- **429 Rate Limit**: Request quota exceeded tracking
- **500 Internal Server Error**: OpenAI service issue detection
- **Generic Errors**: Comprehensive fallback error logging

#### Configurable Logging Levels
- **LOG_LEVEL Environment Variable**: DEBUG, INFO, WARNING, ERROR, CRITICAL
- **Production Ready**: INFO level default with detailed error logging
- **Development Friendly**: DEBUG level for comprehensive operation tracking

### 🔧 Technical Improvements
- **ADDED**: Structured logging format with timestamps and module names
- **ENHANCED**: All parser classes now include detailed operation logging
- **IMPROVED**: Multimodal API call tracking and error diagnosis

---

## [1.1.1] - 2025-06-17

### 🚀 Enhanced Summary Extraction

#### Parallel Summary Processing
- **NEW**: PersonalInfoParser now extracts professional summary alongside personal information
- **PERFORMANCE**: Summary extraction happens in parallel with no additional latency
- **EFFICIENCY**: Single LLM call extracts both personal details and summary
- **ARCHITECTURE**: Summary remains separate in output structure while being processed efficiently

### 🔧 Technical Improvements
- **UPDATED**: PersonalInfoWithSummary model for combined extraction
- **ENHANCED**: Parser prompts to identify professional summaries, objectives, and career highlights
- **IMPROVED**: Test coverage for summary extraction functionality

---

## [1.1.0] - 2025-06-17

### 🚀 Major Performance Improvements

#### Parallel Processing Architecture
- **NEW**: Implemented parallel CV parsing using LangGraph with 7 specialized section parsers
- **PERFORMANCE**: 60-70% faster processing through concurrent section extraction
- **ARCHITECTURE**: Replaced monolithic parser with modular specialized parsers:
  - PersonalInfoParser for contact information and professional summary
  - EducationParser for educational background
  - WorkExperienceParser for job history
  - SkillsParser for technical and soft skills
  - ProjectsParser for project details
  - CertificationsParser for certifications, awards, and publications
  - AdditionalSectionsParser for languages, volunteer work, activities, and references

#### Enhanced Startup Experience
- **NEW**: `main.py` - Advanced Python-based startup script replacing `run.sh`
- **FEATURES**:
  - Cross-platform compatibility (macOS, Linux, Windows)
  - Environment validation and dependency checking
  - Colored output with verbose and quiet modes
  - Auto-install option for missing dependencies
  - Flexible configuration (host, port, reload settings)
  - Comprehensive error handling and user guidance

#### Testing & Quality
- **EXPANDED**: Test suite from 150 to 186 tests (+36 new tests for main.py and parallel processing)
- **COVERAGE**: Comprehensive testing for parallel processing workflow
- **RELIABILITY**: All integration tests updated for new architecture

### 🛠️ Technical Improvements

#### State Management
- **UPDATED**: CVParserState with annotated fields for concurrent processing
- **IMPROVED**: Error handling with proper concurrent error collection
- **ENHANCED**: LangGraph workflow with parallel edges and aggregation node

#### File Structure
- **ADDED**: `.env.example` with configuration examples
- **ADDED**: `tests/test_main.py` with comprehensive tests
- **UPDATED**: Documentation reflecting new architecture and features

### 📚 Documentation Updates
- **ENHANCED**: README.md with performance benchmarks and architecture diagrams
- **ADDED**: Startup script features and usage examples
- **UPDATED**: Installation instructions and prerequisites
- **IMPROVED**: API documentation with parallel processing details

### 🔧 Configuration
- **MAINTAINED**: Full backward compatibility with existing API endpoints
- **PRESERVED**: All existing configuration options and environment variables
- **ENHANCED**: Better error messages and validation

---

## [1.0.0] - 2024-12-16

### Initial Release
- CV parsing API with LangChain and OpenAI GPT-4o-mini
- Support for PDF, DOC, DOCX file formats
- Multimodal parsing capabilities
- Raw text input support
- Unified `/parse` endpoint
- FastAPI framework with automatic documentation
- Comprehensive test suite (186 tests)
- Basic startup script (`run.sh`)

### Core Features
- Personal information extraction
- Education background parsing
- Work experience analysis
- Skills categorization
- Project details extraction
- Certifications and awards processing
- Multiple language support
- Volunteer experience and activities
- Structured Pydantic schema output
