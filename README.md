# CV Parser API

A high-performance API for parsing CV/Resume files using LangChain, LangGraph, and OpenAI's GPT-4o-mini model. Features parallel processing architecture for optimal performance and supports PDF, DOC, and DOCX formats with both text extraction and multimodal parsing capabilities.

## Features

### Core Functionality
- **Multiple File Format Support**: PDF, DOC, and DOCX files
- **Raw Text Support**: Parse CV content directly from text input
- **Enhanced Data Schema**: Returns parsed data with improved structured types for gender and GPA classification
- **Phone Number Formatting**: Automatic formatting of Vietnamese phone numbers to +84-xxxxxxxxx format
- **OpenAI Direct Integration**: Direct GPT-4o-mini parsing with text extraction and multimodal modes
- **🛡️ Intelligent Fallback System**: Automatic fallback from text extraction to multimodal parsing when content quality is poor
- **Two Parsing Modes** (OpenAI):
  - Text extraction mode: Extracts text first, then parses with LLM (with automatic quality validation)
  - Multimodal mode: Direct visual parsing using GPT-4o-mini's vision capabilities
- **PDF to Image Conversion**: Automatic conversion of PDF files to images for multimodal processing
- **Async Processing**: Support for callback-based async processing with intelligent retry logic
- **Comprehensive Schema**: Extracts personal info, education, work experience, skills, projects, certifications, awards, languages, volunteer experience, and more

### Performance & Architecture
- **🚀 Parallel Processing**: Advanced LangGraph workflow with parallel section parsing for 60-70% faster processing
- **⚡ Optimized Latency**: Concurrent extraction of different CV sections (personal info, education, experience, skills, projects, certifications, additional sections)
- **🔧 Modular Design**: Specialized parsers for each CV section enable independent optimization
- **⚙️ Non-blocking I/O**: Async file operations using aiofiles for improved concurrency
- **🆔 Request Tracking**: UUID4-based request correlation across sync and async operations
- **📊 Comprehensive Testing**: 268 tests ensuring reliability and performance

### Developer Experience
- **🛠️ Modern Tools**: Built with FastAPI, LangChain, LangGraph, and Pydantic
- **🎯 Smart Startup Script**: Enhanced Python-based startup with validation, dependency checks, and cross-platform support
- **📖 Auto-Generated Docs**: Interactive API documentation with OpenAPI/Swagger
- **🧪 Extensive Testing**: Complete test suite with edge cases and integration tests
- **🔄 Pre-commit Hooks**: Automated code quality checks with pytest integration
- **⚡ Redis Caching**: Optional Redis caching for improved performance with resilient fallback
- **🔁 Retry Logic**: Intelligent retry mechanism for callback system using tenacity

## Installation

### Prerequisites

- **Python 3.13+** (Required for optimal performance)
- **OpenAI API key** (Get yours at [OpenAI Platform](https://platform.openai.com/api-keys))
- **uv package manager** (Install with: `curl -LsSf https://astral.sh/uv/install.sh | sh`)
- **System dependencies** (required for full functionality):
  - macOS: `brew install poppler antiword`
  - Ubuntu/Debian: `sudo apt-get install poppler-utils antiword`
  - RHEL/CentOS: `sudo yum install poppler-utils antiword`
  - **Note**: `poppler` is required for PDF to image conversion in multimodal mode

### Setup

1. Clone the repository:
```bash
cd adr-cv-parser
```

2. Install Python dependencies using uv:
```bash
# Install uv if you haven't already
curl -LsSf https://astral.sh/uv/install.sh | sh

# Sync dependencies (installs all production dependencies)
uv sync

# Or sync with dev dependencies
uv sync --dev
```

3. Configure environment variables:
```bash
cp .env.example .env
# Edit .env and add your OpenAI API key
```

## Usage

### Starting the API

**Recommended method using the startup script:**
```bash
# Start with default settings (recommended)
uv run python main.py

# Start with custom configuration
uv run python main.py --host 127.0.0.1 --port 9000 --verbose

# Start in production mode (no auto-reload)
uv run python main.py --no-reload --quiet

# View all available options
uv run python main.py --help
```

**Alternative methods:**
```bash
# Using the legacy bash script
./run.sh

# Using uvicorn directly with uv
uv run uvicorn app.api:app --reload --host 0.0.0.0 --port 8080
```

The API will be available at `http://localhost:8080` (or your configured host/port)

### API Endpoints

#### 1. Internal CV Parsing Endpoint (OpenAI-based)

**File Upload (Text Extraction - Default):**
```bash
curl -X POST "http://localhost:8080/internal/v2/parse" \
  -F "file=@your_cv.pdf" \
  -H "accept: application/json"
```

**File Upload (Multimodal Vision Parsing):**
```bash
curl -X POST "http://localhost:8080/internal/v2/parse" \
  -F "file=@your_cv.pdf" \
  -F "multimodal=true" \
  -H "accept: application/json"
```

**Raw Text:**
```bash
curl -X POST "http://localhost:8080/internal/v2/parse" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "John Doe\nSoftware Engineer\nExperience:\n- 5 years Python development\n...",
    "filename": "john_doe_cv.txt"
  }'
```

#### 2. CV Parsing Endpoint with Callback Support

**File Upload with Callback (Async):**
```bash
curl -X POST "http://localhost:8080/internal/v1/parse" \
  -F "file=@your_cv.pdf" \
  -F "callback_url=http://your-app.com/callback" \
  -H "accept: application/json"
```

**File Upload (Sync):**
```bash
curl -X POST "http://localhost:8080/internal/v1/parse" \
  -F "file=@your_cv.pdf" \
  -H "accept: application/json"
```

#### 3. Health Check
```bash
curl "http://localhost:8080/health"
```

#### 4. Mock Callback Endpoint (for testing)
```bash
curl "http://localhost:8080/mock/callback"
```

### Python Client Example

```python
import requests
import json

# Option 1: Parse CV using OpenAI-based parser (text extraction)
with open("resume.pdf", "rb") as f:
    response = requests.post(
        "http://localhost:8080/internal/v2/parse",
        files={"file": ("resume.pdf", f, "application/pdf")}
    )

# Option 2: Parse CV using OpenAI-based parser (multimodal vision)
with open("resume.pdf", "rb") as f:
    response = requests.post(
        "http://localhost:8080/internal/v2/parse",
        files={
            "file": ("resume.pdf", f, "application/pdf"),
            "multimodal": (None, "true")
        }
    )

# Option 3: Parse CV with callback support (async)
with open("resume.pdf", "rb") as f:
    response = requests.post(
        "http://localhost:8080/internal/v1/parse",
        files={
            "file": ("resume.pdf", f, "application/pdf"),
            "callback_url": (None, "http://your-app.com/callback")
        }
    )

# Option 4: Parse CV using raw text (OpenAI-based)
cv_text = """
John Doe
Software Engineer
Email: <EMAIL>
Phone: (*************

Experience:
- 5 years Python development
- Expert in FastAPI and Django
"""

response = requests.post(
    "http://localhost:8080/internal/v2/parse",
    json={
        "text": cv_text,
        "filename": "john_doe_cv.txt"
    }
)

if response.status_code == 200:
    cv_data = response.json()
    print(json.dumps(cv_data, indent=2))
else:
    print(f"Error: {response.status_code} - {response.text}")
```

## API Documentation

Once the server is running, visit:
- Interactive API docs: `http://localhost:8080/docs`
- ReDoc documentation: `http://localhost:8080/redoc`

## Schema

The API returns data following this structure:

```json
{
  "personal_info": {
    "full_name": "string",
    "email": "string",
    "phone": "string",
    "location": "string",
    "linkedin_url": "string",
    ...
  },
  "summary": "string",
  "education": [...],
  "work_experience": [...],
  "skills": [...],
  "projects": [...],
  "certifications": [...],
  "awards": [...],
  "languages": [...],
  "publications": [...],
  "references": [...],
  "volunteer_experience": [...],
  "extracurricular_activities": [...],
  "metadata": {
    "parsed_at": "string",
    "parser_version": "string",
    "source_file_name": "string"
  }
}
```

See `schema.json` for the complete schema definition.

### CV API Response Schema with Request Tracking

The CV parsing endpoint (`/internal/v1/parse`) returns a response structure with request tracking:

```json
{
  "request_id": "f2b0012b-3b31-45f1-a028-04254d9ccc27",
  "status": "completed",
  "success": true,
  "profile": {
    "personal_info": {...},
    "education": [...],
    "work_experience": [...],
    "skills": [...]
  },
  "meta_data": {
    "file_id": "upload_file_id",
    "processing_time": "2.3s"
  }
}
```

**Callback Payload Structure:**
```json
{
  "request_id": "f2b0012b-3b31-45f1-a028-04254d9ccc27",
  "status": "completed",
  "success": true,
  "profile": {...},
  "meta_data": {...},
  "original_filename": "resume.pdf"
}
```

### Phone Number Formatting

The API automatically formats Vietnamese phone numbers to the standardized +84-xxxxxxxxx format using the `phonenumbers` Python library for robust validation and formatting:

**Supported Input Formats:**
- `0987654321` → `+84-987654321` (valid Vietnamese mobile)
- `+84987654321` → `+84-987654321`
- `84987654321` → `+84-987654321`
- `+84 987 654 321` → `+84-987654321` (removes spaces)
- `0123456789` → `+84-123456789` (fallback for invalid patterns)
- `0123-456-789` → `+84-123456789`
- `(0123) 456 789` → `+84-123456789`

**International Numbers:**
- International phone numbers (e.g., `******-123-4567`) are preserved in their original format
- Only Vietnamese phone numbers are automatically reformatted

**Features:**
- **Robust Validation**: Uses Google's `phonenumbers` library for accurate phone number validation
- **Smart Formatting**: Valid Vietnamese numbers are formatted using international standards
- **Fallback Logic**: Invalid patterns are handled with custom formatting rules
- **International Support**: Preserves international phone number formats
- **Error Handling**: Returns `null` for invalid or empty phone numbers
- **Flexible Input**: Handles various separators (spaces, dashes, parentheses, dots)

## Configuration

Edit `app/config.py` or use environment variables:

### OpenAI Integration Settings
- `OPENAI_API_KEY`: Your OpenAI API key (required for `/internal/v2/parse`)
- `OPENAI_MODEL`: Model to use (default: "gpt-4o-mini")
- `MAX_FILE_SIZE`: Maximum file size in bytes (default: 10MB)
- `TEMPERATURE`: LLM temperature for parsing (default: 0.1)
- `ENABLE_MULTIMODAL`: Enable/disable multimodal parsing (default: true)
- `LOG_LEVEL`: Logging level - DEBUG, INFO, WARNING, ERROR, CRITICAL (default: INFO)

### Intelligent Fallback Settings
- `ENABLE_TEXTRACT_FALLBACK`: Enable automatic fallback to multimodal when text extraction fails (default: true)
- `MIN_CONTENT_LENGTH_THRESHOLD`: Minimum characters required for valid content (default: 150)
- `CONTENT_QUALITY_CHECK`: Enable content quality validation (default: true)
- `MAX_SYMBOL_RATIO`: Maximum ratio of symbols to text before considering content garbled (default: 0.3)

### PDF to Image Conversion Settings
- `PDF_TO_IMAGE_DPI`: DPI for PDF to image conversion (default: 200)
- `PDF_MAX_PAGES`: Maximum pages to process in multimodal mode (default: 10)
- `PDF_IMAGE_FORMAT`: Image format - PNG or JPEG (default: PNG)

### Redis Caching Settings (Optional)
- `REDIS_URL`: Redis connection URL for caching (optional, enables caching when set)
- `REDIS_TTL`: Cache TTL in seconds (default: 3600)
- `CACHE_ENABLED`: Enable/disable caching (default: true when Redis URL is set)

## Architecture

### Modular Code Structure
The parser codebase has been refactored into a clean modular architecture for better maintainability and extensibility:

```
app/parser/
├── __init__.py          # Main exports and public API
├── base.py              # Base parser classes and interfaces
├── cv_parser.py         # Main CV parser orchestration
├── models.py            # Data models for parser state
├── sections/            # Specialized section parsers
│   ├── __init__.py
│   ├── personal_info.py # Personal info, education, skills, summary
│   ├── work_experience.py # Work experience and projects
│   └── additional.py    # Certifications, languages, etc.
├── validation/          # Content validation logic
│   ├── __init__.py
│   └── content.py       # Text quality validation
└── workflow/            # LangGraph workflow implementation
    ├── __init__.py
    ├── graph.py         # Workflow graph definition
    └── nodes.py         # Workflow node implementations
```

### Core Technologies
- **FastAPI** for the high-performance web framework
- **LangChain** for LLM interactions and structured output using Pydantic models
- **LangGraph** for orchestrating the parallel parsing workflow
- **Pydantic** for data validation and serialization
- **OpenAI GPT-4o-mini** with structured output for reliable parsing
- **Tenacity** for intelligent retry logic in callback system

### Parallel Processing Workflow
The application uses an advanced parallel processing architecture that now supports both text and multimodal parsing modes:

#### Text Mode Workflow
```
Text Input
    ↓
extract_text_node
    ↓
validate_content_node
    ↓
┌──────────────────────────────────────────────────────────┐
│              Parallel Text Processing                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │
│  │ Personal    │ │ Work Exp &  │ │ Additional  │       │
│  │ Info +      │ │ Projects    │ │ Sections    │       │
│  │ Education + │ │ Parser      │ │ Parser      │       │
│  │ Skills +    │ │             │ │             │       │
│  │ Summary     │ │             │ │             │       │
│  └─────────────┘ └─────────────┘ └─────────────┘       │
└──────────────────────────────────────────────────────────┘
    ↓
aggregate_results_node
    ↓
validation_node
    ↓
Structured CVData Output
```

#### Multimodal Mode Workflow (NEW)
```
PDF/Image Input
    ↓
extract_text_node
    ↓
validate_content_node
    ↓
prepare_images_node (PDF → Images conversion)
    ↓
┌──────────────────────────────────────────────────────────┐
│           Parallel Multimodal Processing                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │
│  │ Personal    │ │ Work Exp &  │ │ Additional  │       │
│  │ Info +      │ │ Projects    │ │ Sections    │       │
│  │ Education + │ │ Vision      │ │ Vision      │       │
│  │ Skills +    │ │ Parser      │ │ Parser      │       │
│  │ Summary     │ │             │ │             │       │
│  │ Vision      │ │             │ │             │       │
│  └─────────────┘ └─────────────┘ └─────────────┘       │
└──────────────────────────────────────────────────────────┘
    ↓
aggregate_results_node
    ↓
validation_node
    ↓
Structured CVData Output
```

### Key Improvements in Multimodal Parsing
- **Split API Calls**: Instead of one large multimodal API call, the parser now makes focused calls for each section
- **Better Accuracy**: Each parser uses specialized prompts optimized for extracting specific sections
- **Parallel Vision Processing**: All three vision parsers run concurrently for optimal performance
- **Shared Image Processing**: PDF to image conversion happens once and images are shared across all parsers
- **Focused Prompts**: Each parser explicitly ignores sections handled by other parsers to avoid duplication

### Callback Integration Workflow
The callback integration provides an alternative async processing path:

```
File Upload → Processing → Workflow Execution
     ↓            ↓              ↓
Temp Storage → File Processing → CV Parsing
     ↓            ↓              ↓
Cleanup ← Callback Response ← Results
```

**Key Features:**
- **Request ID Tracking**: UUID4-based request correlation for better observability and debugging
- **Non-blocking I/O**: Async file operations using aiofiles for improved performance
- **Simplified Architecture**: Removed complex streaming mode for reliable blocking operations
- **Callback Support**: Async processing with webhook notifications
- **Intelligent Retry Logic**: Automatic retry on server errors (5xx), timeouts, and rate limits
- **Error Handling**: Comprehensive error reporting through callbacks
- **Enhanced Logging**: Request ID correlation in logs for better traceability
- **Clean Response Model**: Simplified payload without redundant workflow metadata

### Specialized Parsers (Updated)
The parsers are now organized into three combined modules for optimal performance:

#### 1. PersonalInfoEducationAndSkillsParser
- **Personal Information**: Contact details, LinkedIn/GitHub URLs, date of birth
- **Education**: Schools, degrees, GPAs, graduation dates
- **Skills**: Technical and soft skills categorization
- **Summary**: Professional summary or career objective
- **Multimodal Support**: ✅ Vision-based extraction from images

#### 2. WorkExperienceAndProjectsParser
- **Work Experience**: Job titles, companies, descriptions, achievements
- **Projects**: Personal/professional projects with technologies used
- **Employment Types**: Full-time, internship, freelance, contract
- **Multimodal Support**: ✅ Vision-based extraction from images

#### 3. AdditionalSectionsAndCertificationsParser
- **Languages**: Language proficiencies
- **Certifications**: Professional certifications with issuers and dates
- **Awards**: Achievements and recognitions
- **Publications**: Research papers and articles
- **Volunteer Experience**: Community service and charity work
- **Extracurricular Activities**: Clubs, sports, organizations
- **References**: Professional references with contact info
- **Multimodal Support**: ✅ Vision-based extraction from images

### File Processing
- **PDF**:
  - Text mode: `pdftotext` (poppler-utils) for reliable text extraction with direct command execution
  - Multimodal mode: `pdf2image` with `poppler` for PDF to image conversion
  - **Intelligent Fallback**: Automatic switch to multimodal if text extraction fails or produces poor quality content
- **DOC**: `antiword` for legacy Microsoft Word documents with fallback support
- **DOCX**: `python-docx2txt` for modern Word documents with fallback support
- **Raw Text**: Direct processing without file conversion
- **Quality Validation**: Multi-layered content assessment to ensure extraction quality

### Caching Layer
- **Redis Integration**: Optional Redis caching for improved performance
- **Resilient Fallback**: Automatic fallback to non-cached processing if Redis is unavailable
- **Configurable TTL**: Customizable cache expiration time
- **Hash-based Keys**: Content-based caching keys for efficient storage

### PDF to Image Conversion
For multimodal parsing, PDF files are automatically converted to images:
- **Configurable Quality**: DPI setting controls image resolution (default: 200 DPI)
- **Cost Control**: Maximum pages limit prevents excessive API costs (default: 10 pages)
- **Format Options**: PNG for quality or JPEG for smaller file sizes
- **Error Handling**: Comprehensive error messages for common issues:
  - Missing poppler dependencies
  - Corrupted or invalid PDF files
  - Password-protected PDFs (not supported)
- **Multi-page Support**: All pages sent in a single OpenAI Vision API request

## Error Handling & Logging

### Error Handling
The API provides detailed error messages for:
- Unsupported file types
- File size limits exceeded
- Parsing failures
- Missing API keys
- Invalid file content

### Comprehensive Logging
The application includes detailed logging for monitoring and debugging:

#### **OpenAI API Error Logging**
- **400 Bad Request**: Invalid base64 data or rate limiting issues
- **401 Unauthorized**: API key validation problems
- **429 Rate Limit**: Request quota exceeded
- **500 Internal Server Error**: OpenAI service issues
- **Generic Errors**: Comprehensive error tracking with full stack traces

#### **Parser Operation Logging**
- **INFO Level**: File processing start/completion, workflow execution
- **DEBUG Level**: Detailed parser operations, API call timing, extraction counts
- **ERROR Level**: Full exception details with context (filename, operation type)

#### **Log Configuration**
```bash
# Set log level via environment variable
export LOG_LEVEL=DEBUG  # For detailed debugging
export LOG_LEVEL=INFO   # For production (default)
export LOG_LEVEL=ERROR  # For minimal logging

# Example log output
2025-06-17 13:06:18,102 - app.parser - INFO - Starting multimodal CV parsing for file: resume.pdf
2025-06-17 13:06:18,102 - app.parser - DEBUG - MIME type: application/pdf, base64 length: 168072 characters
2025-06-17 13:06:18,102 - app.parser - DEBUG - Sending request to OpenAI Vision API
2025-06-17 13:06:18,345 - app.parser - INFO - Successfully parsed CV with multimodal for file: resume.pdf
```

#### **API Request Tracking**
Enhanced observability with request ID correlation:

```bash
# Request lifecycle tracking
2025-06-17 14:30:15,123 - app.api - INFO - Processing CV request f2b0012b-3b31-45f1-a028-04254d9ccc27 for file: resume.pdf
2025-06-17 14:30:15,124 - app.api - INFO - [f2b0012b-3b31-45f1-a028-04254d9ccc27] Received CV file for parsing: resume.pdf
2025-06-17 14:30:18,456 - app.api - INFO - [f2b0012b-3b31-45f1-a028-04254d9ccc27] Successfully parsed CV: resume.pdf

# Error tracking with request context
2025-06-17 14:30:20,789 - app.api - ERROR - [f2b0012b-3b31-45f1-a028-04254d9ccc27] Error processing CV resume.pdf: Connection timeout
```

**Benefits:**
- ✅ **Request Correlation**: Match async callbacks to original requests
- ✅ **Debugging Support**: Request ID helps trace issues across logs
- ✅ **Production Monitoring**: Track request lifecycle from start to callback
- ✅ **Performance Analysis**: Identify slow requests and bottlenecks

## Development

### Running Tests
```bash
# Run all tests (268 total)
uv run pytest tests/

# Run tests with coverage
uv run pytest tests/ --cov=app --cov-report=html

# Run specific test modules
uv run pytest tests/test_api.py          # API endpoint tests (24 tests)
uv run pytest tests/test_parser.py       # Parser workflow tests (33 tests)
uv run pytest tests/test_file_processors.py # File processing tests (46 tests, including PDF conversion)
uv run pytest tests/test_fallback_mechanism.py # Fallback system tests (18 tests)
uv run pytest tests/test_main.py         # Startup script tests (48 tests)
uv run pytest tests/test_integration.py  # Integration tests (20 tests)
uv run pytest tests/test_utils.py        # Utility function tests (31 tests, including phone formatting)
uv run pytest tests/test_models.py       # Data model tests (35 tests)

# Run tests with verbose output
uv run pytest tests/ -v

# Test PDF to image conversion specifically
uv run pytest tests/test_file_processors.py::TestPDFToImageConverter -v

# Run linting and formatting
uv run ruff check .
uv run ruff format .
```

### Pre-commit Hooks
The project includes pre-commit configuration for automated code quality:
```bash
# Install pre-commit hooks
pre-commit install

# Run pre-commit manually
pre-commit run --all-files

# The pre-commit hooks include:
# - Ruff linting and formatting
# - Pytest test execution
# - Type checking and validation
```

### Adding New Features
1. Update the Pydantic models in `app/models.py` (enhanced with structured types, improved formatting, and detailed descriptions)
2. Modify the parsing logic in `app/parser.py`
3. Update the API endpoints in `app/api.py`
4. Add comprehensive tests for new functionality
5. Run pre-commit hooks to ensure code quality
6. Update caching configuration if needed for performance optimization

## Performance

### Parallel Processing Benefits
The parallel architecture provides significant performance improvements:

- **60-70% faster processing** compared to sequential parsing
- **Concurrent section extraction**: All CV sections are processed simultaneously, including summary extraction
- **Optimized LLM calls**: Each specialized parser uses focused prompts for better accuracy
- **Scalable design**: Easy to add new section parsers without affecting existing ones
- **Error isolation**: Failures in one section don't affect others

### Benchmarks
- **Small CV (1-2 pages)**: ~2-3 seconds
- **Medium CV (3-4 pages)**: ~4-6 seconds
- **Large CV (5+ pages)**: ~8-12 seconds
- **Multimodal parsing**: +20-30% processing time for visual analysis

*Note: Times may vary based on OpenAI API response times and content complexity*

## Startup Script Features

The `main.py` script provides enhanced functionality compared to the legacy `run.sh`:

### Validation & Checks
- ✅ **Environment validation**: Checks for `.env` file and OpenAI API key
- ✅ **Dependency verification**: Ensures `uv` is installed and syncs dependencies
- ✅ **System dependency checks**: Verifies `pdftotext` and `antiword` availability
- ✅ **Application structure**: Validates all required files are present

### Enhanced User Experience
- 🎨 **Colored output**: Visual indicators for status and errors
- 📝 **Verbose mode**: Detailed debugging information with `--verbose`
- 🔇 **Quiet mode**: Minimal output with `--quiet`
- 🚀 **Auto-install**: Optional automatic dependency installation
- ⚙️ **Flexible configuration**: Customizable host, port, and reload settings

### Cross-Platform Support
- 🍎 **macOS**: Native support with Homebrew install instructions
- 🐧 **Linux**: Support for Ubuntu/Debian and RHEL/CentOS
- 🪟 **Windows**: Cross-platform Python implementation

### Example Usage
```bash
# Development mode with debugging
uv run python main.py --verbose --reload

# Production mode
uv run python main.py --host 0.0.0.0 --port 80 --no-reload --quiet

# Auto-install missing dependencies
uv run python main.py --auto-install

# Custom configuration
uv run python main.py --host 127.0.0.1 --port 9000 --no-color
```

## License

[Add your license here]

## Contributing

[Add contribution guidelines]
